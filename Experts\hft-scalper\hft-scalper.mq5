//+------------------------------------------------------------------+
//|                                   HFT-Scalper.mq5              |
//|                                     Optimized HFT Trading EA    |
//|                                             Your Name Here       |
//+------------------------------------------------------------------+
#property copyright "Your Name Here"
#property link      "https://www.example.com"
#property version   "2.00" // Optimized version with enhanced performance and precision
#property strict

#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>
#include <Trade/OrderInfo.mqh>
#include <Trade/SymbolInfo.mqh>

// System information class for better organization and performance
class SystemInfo {
private:
    string          m_symbol;
    double          m_point;
    int             m_digits;
    int             m_stopLevel;
    double          m_tickSize;
    double          m_tickValue;
    double          m_minVolume;
    double          m_maxVolume;
    double          m_volumeStep;

public:
    // Constructor
    SystemInfo(string symbol) {
        m_symbol = symbol;
        Refresh();
    }

    // Update all symbol information
    void Refresh() {
        m_point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
        m_digits = (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS);
        m_stopLevel = (int)SymbolInfoInteger(m_symbol, SYMBOL_TRADE_STOPS_LEVEL);
        m_tickSize = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_SIZE);
        m_tickValue = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_VALUE);
        m_minVolume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
        m_maxVolume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
        m_volumeStep = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
    }

    // Getters
    double Point() const { return m_point; }
    int Digits() const { return m_digits; }
    int StopLevel() const { return m_stopLevel; }
    double TickSize() const { return m_tickSize; }
    double TickValue() const { return m_tickValue; }
    double MinVolume() const { return m_minVolume; }
    double MaxVolume() const { return m_maxVolume; }
    double VolumeStep() const { return m_volumeStep; }

    // Utility functions
    double NormalizePrice(double price) const {
        return NormalizeDouble(price, m_digits);
    }

    double GetMinStopDistance() const {
        return (m_stopLevel + 3) * m_point; // Add 3 points buffer for safety
    }

    bool IsValidLotSize(double lots) const {
        if(lots < m_minVolume || (m_maxVolume > 0 && lots > m_maxVolume))
            return false;

        double remainder = MathMod(lots, m_volumeStep);
        if(remainder > m_volumeStep * 0.0001 && remainder < m_volumeStep * 0.9999)
            return false;

        return true;
    }
};

// Structure for batch emergency close operations
struct EmergencyClosePosition
{
   ulong             ticket;           // Position ticket
   ENUM_POSITION_TYPE type;            // Position type (buy/sell)
   double            volume;           // Position volume
   double            openPrice;        // Position open price
   double            currentSL;        // Current stop loss
   double            profit;           // Current profit
   double            priceDistance;    // Distance from current price to SL
   bool              needsClose;       // Flag indicating if position needs emergency close
};

// Global objects
CTrade trade;
CPositionInfo pos;
COrderInfo ord;
CSymbolInfo symInfo;
SystemInfo* sysInfo;

// Forward declarations
bool IsMarketFrozen();
void SortPositionsByProfit(EmergencyClosePosition &positions[], int count);
bool ClosePositionWithRetries(EmergencyClosePosition &posData, double ask, double bid, int emergencySlippage, bool isMarketFrozen);

//--- Enums for configuration
enum ENUM_SL_MODE // General SL Mode Enum
{
   SL_MODE_POINTS,    // SL based on fixed points
   SL_MODE_ATR,       // SL based on ATR (Primarily for Initial SL)
   SL_MODE_PERCENTAGE, // SL based on percentage of price
   SL_MODE_ADAPTIVE   // Adaptive SL based on market volatility
};

// Enums for EA optimization
enum ENUM_TRADE_EXECUTION_MODE
{
   EXECUTION_MODE_ASYNC,    // Asynchronous (faster but less reliable)
   EXECUTION_MODE_SYNC,     // Synchronous (slower but more reliable)
   EXECUTION_MODE_HYBRID    // Hybrid (async for non-critical, sync for critical operations)
};

enum ENUM_ORDER_VERIFICATION
{
   VERIFICATION_BASIC,      // Basic verification (faster)
   VERIFICATION_ENHANCED,   // Enhanced with retries (more reliable)
   VERIFICATION_ADAPTIVE    // Adaptive verification based on market conditions
};

enum ENUM_STATE_MANAGEMENT
{
   STATE_SIMPLE,            // Simple state management
   STATE_MACHINE,           // State machine approach (more reliable)
   STATE_ADVANCED           // Advanced state machine with recovery mechanisms
};

enum ENUM_TREND_FILTER
{
   TREND_FILTER_OFF,        // No trend filtering
   TREND_FILTER_MA,         // Moving Average trend filter
   TREND_FILTER_ADX,        // ADX trend filter
   TREND_FILTER_BOTH,       // Both MA and ADX filters
   TREND_FILTER_ADAPTIVE    // Adaptive filter based on market conditions
};

enum ENUM_EXHAUSTION_DETECTION
{
   EXHAUSTION_OFF,          // No exhaustion detection
   EXHAUSTION_RSI,          // RSI-based exhaustion detection
   EXHAUSTION_STOCH,        // Stochastic-based exhaustion detection
   EXHAUSTION_BOTH,         // Both RSI and Stochastic
   EXHAUSTION_ADVANCED      // Advanced with volume analysis
};

enum ENUM_VOLATILITY_REGIME
{
   VOLATILITY_NORMAL,       // Normal volatility regime
   VOLATILITY_HIGH,         // High volatility regime
   VOLATILITY_LOW,          // Low volatility regime
   VOLATILITY_ADAPTIVE      // Adaptive based on ATR
};

enum ENUM_MARKET_HOURS
{
   MARKET_HOURS_ALL,        // Trade during all market hours
   MARKET_HOURS_ACTIVE,     // Trade only during active market hours
   MARKET_HOURS_CUSTOM      // Trade during custom hours
};

//--- Input Parameters ---
input group "Order Placement & Volume"
// Ensure 'Lots' meets your broker's minimum and step volume for US30 (e.g., min 0.1)
input double Lots                         = 0.1;    // Trade Volume
input int    OrderDistancePoints          = 1500;   // Distance for pending orders from current price (in points)
input bool   AdaptiveOrderDistance        = true;   // Adjust order distance based on market volatility
input double OrderDistanceATRMultiplier   = 1.0;    // Multiplier for ATR to set order distance (if adaptive)
input ulong  MagicNumber                  = 202403; // EA's Unique Magic Number
input int    Slippage                     = 100;    // Max allowed slippage for order execution (in points)
input string OrderComment                 = "HFT-Scalper_v2.0";

input group "Initial Stop Loss Configuration"
input ENUM_SL_MODE InitialSLMode          = SL_MODE_ATR;    // Mode for initial SL of newly opened positions
input int    InitialSLPoints              = 1000;           // Initial SL in points (if SL_MODE_POINTS)
input double InitialSLPercentage          = 0.5;            // Initial SL as % of open price (e.g., 0.5 for 0.5%) (if SL_MODE_PERCENTAGE)
input ENUM_TIMEFRAMES AtrTimeframe        = PERIOD_M5;      // Timeframe for ATR calculation
input int    AtrPeriod                    = 14;             // Period for ATR indicator
input double AtrMultiplier                = 1.5;            // Multiplier for ATR value to set SL (if SL_MODE_ATR)
input double AdaptiveSLFactor             = 1.2;            // Factor for adaptive SL adjustment (if SL_MODE_ADAPTIVE)

input group "Trailing Stop Loss (For Active Positions)"
input ENUM_SL_MODE TrailingSLMode         = SL_MODE_POINTS; // Mode for trailing SL
input int    TrailingStopLossActivePoints = 500;            // Trailing SL distance for open positions (in points, if SL_MODE_POINTS)
input double TrailingSLPercentage         = 0.25;           // Trailing SL as % of current price (e.g., 0.25 for 0.25%) (if SL_MODE_PERCENTAGE)
input double TrailingStepPoints           = 50;             // Minimum price movement to adjust trailing stop (in points)
input bool   UseBreakevenBuffer           = true;           // Enable buffer for breakeven stop loss
input int    BreakevenBuffer              = 10;             // Buffer in points to add to entry price when setting breakeven SL
input bool   FastTrailingExit             = true;           // Enable faster exit after trailing stop is hit
input int    FastTrailingPoints           = 20;             // Points to tighten trailing stop after each adjustment
input bool   ProfitProtectionEnabled      = true;           // Prevent trailing stop from closing positions at a loss
input int    MinimumProfitBuffer          = 5;              // Minimum profit in points to maintain after trailing is activated

input group "Emergency Exit Settings"
input bool   EmergencyCloseEnabled        = true;           // Enable emergency close when price crosses SL
input int    MaxSlippagePoints            = 20;             // Maximum allowed slippage for emergency close (in points)
input int    EmergencyCloseBuffer         = 3;              // Buffer in points to detect SL crossing early
input int    EmergencyRetryCount          = 5;              // Number of retries for emergency close
input int    EmergencyRetryDelayMs        = 100;            // Delay between retries in milliseconds
input bool   AdaptiveEmergencySlippage    = true;           // Increase slippage during high volatility
input int    MaxAdaptiveSlippagePoints    = 50;             // Maximum slippage during high volatility (in points)
input bool   UseAlternativeCloseMethod    = true;           // Try alternative close method if primary fails
input bool   DetectMarketFreeze           = true;           // Detect and handle market freeze conditions
input int    FreezeDetectionMs            = 500;            // Time in milliseconds to consider market frozen
input int    FreezeMaxRetries             = 10;             // Maximum retries during market freeze
input bool   BatchEmergencyClose          = true;           // Close all qualifying positions simultaneously
input bool   PrioritizeByProfit           = true;           // Prioritize positions by profit when closing

input group "Market Conditions & Trading Hours"
input ENUM_VOLATILITY_REGIME VolatilityRegime = VOLATILITY_ADAPTIVE; // Volatility regime for adaptive parameters
input ENUM_MARKET_HOURS MarketHoursMode    = MARKET_HOURS_ALL;    // Market hours trading mode
input int    ActiveStartHour               = 8;                   // Start hour for active market hours (0-23)
input int    ActiveEndHour                 = 20;                  // End hour for active market hours (0-23)
input bool   MondayFilter                  = false;               // Filter out Monday trading
input bool   FridayFilter                  = false;               // Filter out Friday trading
input int    FridayEndHour                 = 16;                  // Hour to stop trading on Friday (0-23)

input group "Entry Filters"
input ENUM_TREND_FILTER TrendFilterMode    = TREND_FILTER_BOTH;   // Trend filter mode
input ENUM_TIMEFRAMES   TrendTimeframe     = PERIOD_H1;           // Timeframe for trend analysis
input int    FastMA_Period                 = 20;                  // Fast MA period
input int    SlowMA_Period                 = 50;                  // Slow MA period
input ENUM_MA_METHOD MA_Method             = MODE_EMA;            // MA method
input int    ADX_Period                    = 14;                  // ADX period
input double ADX_Threshold                 = 25;                  // Minimum ADX value for trend strength
input bool   UseVolumeTrendFilter          = false;               // Use volume for trend confirmation
input int    VolumeMAPeriod                = 20;                  // Volume Moving Average period

input group "Exhaustion Detection"
input ENUM_EXHAUSTION_DETECTION ExhaustionMode = EXHAUSTION_BOTH; // Exhaustion detection mode
input ENUM_TIMEFRAMES ExhaustionTimeframe = PERIOD_M15;           // Timeframe for exhaustion detection
input int    RSI_Period                    = 14;                  // RSI period
input double RSI_UpperThreshold            = 70;                  // RSI upper threshold (overbought)
input double RSI_LowerThreshold            = 30;                  // RSI lower threshold (oversold)
input int    Stoch_KPeriod                 = 14;                  // Stochastic %K period
input int    Stoch_DPeriod                 = 3;                   // Stochastic %D period
input int    Stoch_Slowing                 = 3;                   // Stochastic slowing
input double Stoch_UpperThreshold          = 80;                  // Stochastic upper threshold (overbought)
input double Stoch_LowerThreshold          = 20;                  // Stochastic lower threshold (oversold)
input bool   CancelOrdersOnExhaustion      = true;                // Cancel pending orders on exhaustion signals
input bool   CheckPriceActionPatterns      = true;                // Check for price action reversal patterns
input int    PriceActionBars               = 5;                   // Number of bars to check for price action patterns
input bool   UseVolumeForExhaustion        = false;               // Use volume for exhaustion confirmation

input group "EA Optimization Settings"
input ENUM_TRADE_EXECUTION_MODE ExecutionMode = EXECUTION_MODE_HYBRID; // Trade execution mode
input ENUM_ORDER_VERIFICATION   VerificationMode = VERIFICATION_ENHANCED; // Order verification strategy
input ENUM_STATE_MANAGEMENT     StateManagement = STATE_MACHINE;  // State management approach
input int    CooldownSeconds                = 2;                  // Cooldown period between order operations (seconds)
input bool   AdaptiveCooldown               = true;               // Adjust cooldown based on market conditions
input int    MaxRetries                     = 3;                  // Maximum number of retries for order operations
input int    RetryDelayMs                   = 100;                // Delay between retries (milliseconds)
input bool   DEBUG                          = false;              // Enable detailed debug logging
input bool   LogTradeDetails                = true;               // Log trade execution details (even without DEBUG)

//--- Global Variables ---
long   buyStopTicket  = 0;
long   sellStopTicket = 0;
double point;
int    symbolDigits;  // Renamed to avoid conflicts with library variables

// Indicator handles
int    atrHandle = INVALID_HANDLE;
int    fastMAHandle = INVALID_HANDLE;
int    slowMAHandle = INVALID_HANDLE;
int    adxHandle = INVALID_HANDLE;
int    rsiHandle = INVALID_HANDLE;
int    stochHandle = INVALID_HANDLE;
int    volumeMAHandle = INVALID_HANDLE;

// Market data
MqlTick latestTick;
int    stopLevelPoints;
double minVolume;
double volumeStep;
double maxVolume;

// Adaptive parameters
double currentATR = 0.0;
double adaptiveOrderDistance = 0.0;
double adaptiveSLDistance = 0.0;
ENUM_VOLATILITY_REGIME currentVolatilityRegime = VOLATILITY_NORMAL;
int    adaptiveCooldownSeconds = 0;
bool   isMarketHoursActive = true;

// Performance metrics
int    totalOrdersPlaced = 0;
int    successfulOrders = 0;
int    failedOrders = 0;
int    orderModifications = 0;
datetime lastPerformanceReport = 0;

// Enhanced state management variables
enum ENUM_EA_STATE {
   STATE_NORMAL,
   STATE_WAITING_FOR_BUY_CONFIRMATION,
   STATE_WAITING_FOR_SELL_CONFIRMATION,
   STATE_PROCESSING_TRANSACTION,
   STATE_ERROR_RECOVERY,
   STATE_MARKET_MONITORING
};
ENUM_EA_STATE currentState = STATE_NORMAL;
ENUM_EA_STATE previousState = STATE_NORMAL;
datetime stateChangeTime = 0;
datetime lastBuyOrderTime = 0;
datetime lastSellOrderTime = 0;
datetime lastStateTransition = 0;
bool isProcessingTransaction = false;
int consecutiveErrors = 0;

// Enhanced order tracking structures
struct OrderInfo {
   long ticket;
   datetime creationTime;
   datetime lastUpdateTime;
   double price;
   double stopLoss;
   double takeProfit;
   ENUM_ORDER_STATE lastKnownState;
   int modificationAttempts;
   bool isBeingModified;
};

// Structure for batch emergency close operations is defined at the top of the file
OrderInfo buyStopInfo;
OrderInfo sellStopInfo;

// Market condition tracking
struct MarketCondition {
   bool isTrendUp;
   bool isStrongTrend;
   bool isOverbought;
   bool isOversold;
   double volatility;
   datetime lastUpdate;
};
MarketCondition marketCondition;

// Structure to track positions that have reached trailing stop activation threshold
struct TrailingPositionInfo {
   ulong ticket;                // Position ticket
   bool trailingActivated;      // Flag indicating if trailing has been activated for this position
   double activationPrice;      // Price at which trailing was activated
   datetime activationTime;     // Time when trailing was activated
};
// Map to store trailing position information (ticket -> info)
TrailingPositionInfo trailingPositions[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize SystemInfo class
   sysInfo = new SystemInfo(_Symbol);
   if(sysInfo == NULL)
   {
      Print("Failed to initialize SystemInfo. EA terminated.");
      return(INIT_FAILED);
   }

   // Initialize symbol info object
   symInfo.Name(_Symbol);
   symInfo.RefreshRates();

   // Initialize trade object with appropriate settings
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetDeviationInPoints(Slippage);
   trade.SetTypeFillingBySymbol(_Symbol);

   // Set execution mode based on input parameter
   switch(ExecutionMode)
   {
      case EXECUTION_MODE_ASYNC:
         trade.SetAsyncMode(true);
         break;
      case EXECUTION_MODE_SYNC:
         trade.SetAsyncMode(false);
         break;
      case EXECUTION_MODE_HYBRID:
         // Default to async, will toggle as needed for critical operations
         trade.SetAsyncMode(true);
         break;
   }

   // Initialize state variables
   currentState = STATE_NORMAL;
   previousState = STATE_NORMAL;
   stateChangeTime = 0;
   lastBuyOrderTime = 0;
   lastSellOrderTime = 0;
   lastStateTransition = 0;
   isProcessingTransaction = false;
   consecutiveErrors = 0;

   // Initialize order tracking structures
   ResetOrderInfo(buyStopInfo);
   ResetOrderInfo(sellStopInfo);

   // Initialize market condition tracking
   marketCondition.isTrendUp = false;
   marketCondition.isStrongTrend = false;
   marketCondition.isOverbought = false;
   marketCondition.isOversold = false;
   marketCondition.volatility = 0.0;
   marketCondition.lastUpdate = 0;

   // Initialize performance metrics
   totalOrdersPlaced = 0;
   successfulOrders = 0;
   failedOrders = 0;
   orderModifications = 0;
   lastPerformanceReport = 0;

   // Store symbol properties for quick access
   point = sysInfo.Point();
   symbolDigits = sysInfo.Digits();
   stopLevelPoints = sysInfo.StopLevel();
   minVolume = sysInfo.MinVolume();
   volumeStep = sysInfo.VolumeStep();
   maxVolume = sysInfo.MaxVolume();

   // Validate lot size
   if(!sysInfo.IsValidLotSize(Lots))
   {
      PrintFormat("Input 'Lots' (%.2f) is invalid for %s. Min: %.2f, Max: %.2f, Step: %.2f. EA terminated.",
                  Lots, _Symbol, minVolume, maxVolume, volumeStep);
      return(INIT_PARAMETERS_INCORRECT);
   }

   // Initialize adaptive parameters
   adaptiveCooldownSeconds = CooldownSeconds;

   // Initialize indicators
   if(!InitializeIndicators())
   {
      Print("Failed to initialize all required indicators. EA will continue but some features may not work properly.");
   }

   // Validate order distance
   int minOrderDistance = stopLevelPoints + 10; // Small buffer
   if(OrderDistancePoints <= minOrderDistance)
   {
      PrintFormat("OrderDistancePoints (%d) too small for %s. Min (StopLevel + buffer): %d. EA terminated.",
                  OrderDistancePoints, _Symbol, minOrderDistance);
      return(INIT_PARAMETERS_INCORRECT);
   }

   // Validate stop loss parameters
   if(!ValidateStopLossParameters())
   {
      return(INIT_PARAMETERS_INCORRECT);
   }

   // Check for existing orders
   CheckExistingOrders();

   // Get initial market data
   if(!SymbolInfoTick(_Symbol, latestTick))
   {
      PrintFormat("Failed to get initial tick for %s.", _Symbol);
      // Continue anyway, will get tick in OnTick
   }

   // Update market conditions
   UpdateMarketConditions();

   // Log initialization details
   if(DEBUG)
   {
      PrintFormat("%s Initialized. Symbol: %s, Point: %f, Digits: %d, Magic: %d",
                 OrderComment, _Symbol, point, symbolDigits, MagicNumber);
      PrintFormat("Volume: Lots=%.2f, MinVol=%.2f, MaxVol=%.2f, VolStep=%.2f",
                 Lots, minVolume, maxVolume, volumeStep);

      PrintFormat("Initial SL Mode: %s", EnumToString(InitialSLMode));
      if(InitialSLMode == SL_MODE_ATR)
         PrintFormat("ATR Config: TF=%s, Period=%d, Multiplier=%.2f. Handle: %d",
                    EnumToString(AtrTimeframe), AtrPeriod, AtrMultiplier, atrHandle);
      else if(InitialSLMode == SL_MODE_POINTS)
         PrintFormat("Initial SL Points: %d", InitialSLPoints);
      else if(InitialSLMode == SL_MODE_PERCENTAGE)
         PrintFormat("Initial SL Percentage: %.3f%% of Open Price", InitialSLPercentage);
      else // SL_MODE_ADAPTIVE
         PrintFormat("Adaptive SL Factor: %.2f", AdaptiveSLFactor);

      PrintFormat("Trailing SL Mode: %s", EnumToString(TrailingSLMode));
      if(TrailingSLMode == SL_MODE_POINTS)
         PrintFormat("Trailing SL Points: %d pts", TrailingStopLossActivePoints);
      else if(TrailingSLMode == SL_MODE_PERCENTAGE)
         PrintFormat("Trailing SL Percentage: %.3f%% of Current Price", TrailingSLPercentage);
      else // SL_MODE_ADAPTIVE or SL_MODE_ATR
         PrintFormat("Adaptive Trailing Settings: Factor=%.2f, Step=%.2f pts",
                    AdaptiveSLFactor, TrailingStepPoints);

      PrintFormat("Breakeven Buffer: %s (%d pts), Fast Trailing Exit: %s (%d pts)",
                 UseBreakevenBuffer ? "Enabled" : "Disabled", BreakevenBuffer,
                 FastTrailingExit ? "Enabled" : "Disabled", FastTrailingPoints);

      PrintFormat("Profit Protection: %s (Min Profit Buffer: %d pts)",
                 ProfitProtectionEnabled ? "Enabled" : "Disabled", MinimumProfitBuffer);

      PrintFormat("Emergency Close: %s, Buffer: %d pts, Retries: %d (delay: %d ms)",
                 EmergencyCloseEnabled ? "Enabled" : "Disabled", EmergencyCloseBuffer,
                 EmergencyRetryCount, EmergencyRetryDelayMs);

      PrintFormat("Emergency Slippage: %d pts (Adaptive: %s, Max: %d pts), Alt Method: %s",
                 MaxSlippagePoints, AdaptiveEmergencySlippage ? "Enabled" : "Disabled",
                 MaxAdaptiveSlippagePoints, UseAlternativeCloseMethod ? "Enabled" : "Disabled");

      PrintFormat("Market Freeze Detection: %s, Detection Time: %d ms, Max Retries: %d",
                 DetectMarketFreeze ? "Enabled" : "Disabled", FreezeDetectionMs, FreezeMaxRetries);

      PrintFormat("Batch Emergency Close: %s, Prioritize by Profit: %s",
                 BatchEmergencyClose ? "Enabled" : "Disabled", PrioritizeByProfit ? "Enabled" : "Disabled");

      PrintFormat("Distances: Order=%d pts, StopLevel=%d pts", OrderDistancePoints, stopLevelPoints);

      // Log optimization settings
      PrintFormat("Optimization Settings: ExecutionMode=%s, VerificationMode=%s, StateManagement=%s",
                 EnumToString(ExecutionMode), EnumToString(VerificationMode), EnumToString(StateManagement));
      PrintFormat("Cooldown=%d sec, MaxRetries=%d, RetryDelay=%d ms",
                 CooldownSeconds, MaxRetries, RetryDelayMs);
   }
   else if(LogTradeDetails)
   {
      // Minimal initialization message
      Print(OrderComment, " initialized on ", _Symbol);
   }

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Initialize all required indicators                               |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
   bool allSuccessful = true;

   // Initialize ATR indicator (used for multiple purposes)
   atrHandle = iATR(_Symbol, AtrTimeframe, AtrPeriod);
   if(atrHandle == INVALID_HANDLE)
   {
      PrintFormat("Error creating ATR indicator (code: %d). Adaptive features may not work properly.", GetLastError());
      allSuccessful = false;
   }

   // Initialize trend filter indicators if enabled
   if(TrendFilterMode == TREND_FILTER_MA || TrendFilterMode == TREND_FILTER_BOTH || TrendFilterMode == TREND_FILTER_ADAPTIVE)
   {
      fastMAHandle = iMA(_Symbol, TrendTimeframe, FastMA_Period, 0, MA_Method, PRICE_CLOSE);
      slowMAHandle = iMA(_Symbol, TrendTimeframe, SlowMA_Period, 0, MA_Method, PRICE_CLOSE);

      if(fastMAHandle == INVALID_HANDLE || slowMAHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating MA indicators (code: %d). Trend filtering may not work properly.", GetLastError());
         allSuccessful = false;
      }
   }

   if(TrendFilterMode == TREND_FILTER_ADX || TrendFilterMode == TREND_FILTER_BOTH || TrendFilterMode == TREND_FILTER_ADAPTIVE)
   {
      adxHandle = iADX(_Symbol, TrendTimeframe, ADX_Period);

      if(adxHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating ADX indicator (code: %d). Trend filtering may not work properly.", GetLastError());
         allSuccessful = false;
      }
   }

   // Initialize exhaustion detection indicators if enabled
   if(ExhaustionMode == EXHAUSTION_RSI || ExhaustionMode == EXHAUSTION_BOTH || ExhaustionMode == EXHAUSTION_ADVANCED)
   {
      rsiHandle = iRSI(_Symbol, ExhaustionTimeframe, RSI_Period, PRICE_CLOSE);

      if(rsiHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating RSI indicator (code: %d). Exhaustion detection may not work properly.", GetLastError());
         allSuccessful = false;
      }
   }

   if(ExhaustionMode == EXHAUSTION_STOCH || ExhaustionMode == EXHAUSTION_BOTH || ExhaustionMode == EXHAUSTION_ADVANCED)
   {
      stochHandle = iStochastic(_Symbol, ExhaustionTimeframe, Stoch_KPeriod, Stoch_DPeriod, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);

      if(stochHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating Stochastic indicator (code: %d). Exhaustion detection may not work properly.", GetLastError());
         allSuccessful = false;
      }
   }

   // Initialize volume MA if volume-based filters are enabled
   if(UseVolumeTrendFilter || UseVolumeForExhaustion)
   {
      volumeMAHandle = iMA(_Symbol, ExhaustionTimeframe, VolumeMAPeriod, 0, MODE_SMA, VOLUME_REAL);

      if(volumeMAHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating Volume MA indicator (code: %d). Volume-based filtering may not work properly.", GetLastError());
         allSuccessful = false;
      }
   }

   return allSuccessful;
}

//+------------------------------------------------------------------+
//| Validate stop loss parameters                                    |
//+------------------------------------------------------------------+
bool ValidateStopLossParameters()
{
   // Validate initial stop loss parameters
   if(InitialSLMode == SL_MODE_POINTS && InitialSLPoints <= stopLevelPoints)
   {
      PrintFormat("InitialSLPoints (%d) too small. Min (StopLevel): %d. EA terminated.",
                  InitialSLPoints, stopLevelPoints);
      return false;
   }

   if(InitialSLMode == SL_MODE_PERCENTAGE && (InitialSLPercentage <= 0.001 || InitialSLPercentage > 50.0))
   {
      PrintFormat("InitialSLPercentage (%.3f%%) is out of reasonable range (0.001%%-50%%). EA terminated.", InitialSLPercentage);
      return false;
   }

   // Validate trailing stop loss parameters
   if(TrailingSLMode == SL_MODE_POINTS && TrailingStopLossActivePoints <= stopLevelPoints)
   {
      PrintFormat("TrailingStopLossActivePoints (%d) too small. Min (StopLevel): %d. EA terminated.",
                  TrailingStopLossActivePoints, stopLevelPoints);
      return false;
   }

   if(TrailingSLMode == SL_MODE_PERCENTAGE && (TrailingSLPercentage <= 0.001 || TrailingSLPercentage > 50.0))
   {
      PrintFormat("TrailingSLPercentage (%.3f%%) is out of reasonable range (0.001%%-50%%). EA terminated.", TrailingSLPercentage);
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Reset order info structure to default values                     |
//+------------------------------------------------------------------+
void ResetOrderInfo(OrderInfo &info)
{
   info.ticket = 0;
   info.creationTime = 0;
   info.lastUpdateTime = 0;
   info.price = 0;
   info.stopLoss = 0;
   info.takeProfit = 0;
   info.lastKnownState = ORDER_STATE_STARTED;
   info.modificationAttempts = 0;
   info.isBeingModified = false;
}

//+------------------------------------------------------------------+
//| Find trailing position info by ticket                             |
//+------------------------------------------------------------------+
int FindTrailingPosition(ulong ticket)
{
   int size = ArraySize(trailingPositions);
   for(int i = 0; i < size; i++)
   {
      if(trailingPositions[i].ticket == ticket)
         return i;
   }
   return -1; // Not found
}

//+------------------------------------------------------------------+
//| Add or update trailing position info                              |
//+------------------------------------------------------------------+
void UpdateTrailingPosition(ulong ticket, bool activated, double price)
{
   int index = FindTrailingPosition(ticket);

   if(index >= 0)
   {
      // Update existing record
      trailingPositions[index].trailingActivated = activated;
      if(activated && trailingPositions[index].activationPrice == 0)
      {
         // First time activation
         trailingPositions[index].activationPrice = price;
         trailingPositions[index].activationTime = TimeCurrent();

         if(DEBUG || LogTradeDetails)
            PrintFormat("Trailing activated for position #%d at price %s", ticket, DoubleToString(price, symbolDigits));
      }
   }
   else
   {
      // Add new record
      int size = ArraySize(trailingPositions);
      ArrayResize(trailingPositions, size + 1);

      trailingPositions[size].ticket = ticket;
      trailingPositions[size].trailingActivated = activated;
      trailingPositions[size].activationPrice = activated ? price : 0;
      trailingPositions[size].activationTime = activated ? TimeCurrent() : 0;

      if(activated && DEBUG)
         PrintFormat("New trailing position added and activated for #%d at price %s", ticket, DoubleToString(price, symbolDigits));
   }
}

//+------------------------------------------------------------------+
//| Remove trailing position info                                     |
//+------------------------------------------------------------------+
void RemoveTrailingPosition(ulong ticket)
{
   int index = FindTrailingPosition(ticket);
   if(index < 0)
      return; // Not found

   int size = ArraySize(trailingPositions);

   // Move the last element to the position of the removed element
   if(index < size - 1)
      trailingPositions[index] = trailingPositions[size - 1];

   // Resize the array to remove the last element
   ArrayResize(trailingPositions, size - 1);

   if(DEBUG)
      PrintFormat("Trailing position #%d removed from tracking", ticket);
}

//+------------------------------------------------------------------+
//| Clean up closed positions from trailing tracking                  |
//+------------------------------------------------------------------+
void CleanupTrailingPositions()
{
   int size = ArraySize(trailingPositions);
   if(size == 0)
      return;

   // Create a temporary array to store positions to keep
   TrailingPositionInfo tempArray[];
   ArrayResize(tempArray, 0);

   for(int i = 0; i < size; i++)
   {
      // Check if position still exists
      if(PositionSelectByTicket(trailingPositions[i].ticket))
      {
         // Position exists, add to temp array
         int tempSize = ArraySize(tempArray);
         ArrayResize(tempArray, tempSize + 1);
         tempArray[tempSize] = trailingPositions[i];
      }
      else if(DEBUG)
      {
         PrintFormat("Removing closed position #%d from trailing tracking", trailingPositions[i].ticket);
      }
   }

   // Replace original array with temp array
   ArrayFree(trailingPositions);
   int tempSize = ArraySize(tempArray);
   ArrayResize(trailingPositions, tempSize);

   for(int i = 0; i < tempSize; i++)
      trailingPositions[i] = tempArray[i];
}

//+------------------------------------------------------------------+
//| Update market conditions based on indicators                      |
//+------------------------------------------------------------------+
void UpdateMarketConditions()
{
   // Update ATR value for volatility measurement
   double atrValue = GetAtrValue();
   if(atrValue > 0)
   {
      marketCondition.volatility = atrValue;

      // Determine volatility regime
      if(VolatilityRegime == VOLATILITY_ADAPTIVE)
      {
         // Calculate average ATR over multiple periods for comparison
         double atrBuffer[];
         ArraySetAsSeries(atrBuffer, true);

         if(CopyBuffer(atrHandle, 0, 0, 20, atrBuffer) > 0)
         {
            double avgATR = 0;
            for(int i = 0; i < 20; i++)
               avgATR += atrBuffer[i];
            avgATR /= 20;

            // Determine regime based on current ATR vs average
            if(atrValue > avgATR * 1.5)
               currentVolatilityRegime = VOLATILITY_HIGH;
            else if(atrValue < avgATR * 0.7)
               currentVolatilityRegime = VOLATILITY_LOW;
            else
               currentVolatilityRegime = VOLATILITY_NORMAL;
         }
      }
      else
      {
         currentVolatilityRegime = VolatilityRegime; // Use user-defined regime
      }

      // Calculate adaptive parameters
      if(AdaptiveOrderDistance)
      {
         adaptiveOrderDistance = atrValue * OrderDistanceATRMultiplier;
         // Ensure minimum distance
         if(adaptiveOrderDistance < (stopLevelPoints + 10) * point)
            adaptiveOrderDistance = (stopLevelPoints + 10) * point;
      }

      // Adaptive stop loss distance
      if(InitialSLMode == SL_MODE_ADAPTIVE || TrailingSLMode == SL_MODE_ADAPTIVE)
      {
         adaptiveSLDistance = atrValue * AdaptiveSLFactor;
         // Ensure minimum distance
         if(adaptiveSLDistance < (stopLevelPoints + 5) * point)
            adaptiveSLDistance = (stopLevelPoints + 5) * point;
      }

      // Adaptive cooldown
      if(AdaptiveCooldown)
      {
         switch(currentVolatilityRegime)
         {
            case VOLATILITY_HIGH:
               adaptiveCooldownSeconds = CooldownSeconds * 2; // Longer cooldown in high volatility
               break;
            case VOLATILITY_LOW:
               adaptiveCooldownSeconds = MathMax(1, CooldownSeconds / 2); // Shorter cooldown in low volatility
               break;
            default: // VOLATILITY_NORMAL
               adaptiveCooldownSeconds = CooldownSeconds;
               break;
         }
      }
   }

   // Update trend information
   bool adxUp = false;
   marketCondition.isStrongTrend = IsADXTrendStrong(adxUp);
   marketCondition.isTrendUp = IsMATrendUp();

   // Update exhaustion information
   marketCondition.isOverbought = IsUpwardExhaustion();
   marketCondition.isOversold = IsDownwardExhaustion();

   // Update market hours status
   MqlDateTime currentTime;
   TimeToStruct(TimeCurrent(), currentTime);
   int currentHour = currentTime.hour;
   int currentDay = currentTime.day_of_week; // 0-Sunday, 1-Monday, ..., 5-Friday, 6-Saturday

   // Check trading hours
   switch(MarketHoursMode)
   {
      case MARKET_HOURS_ACTIVE:
         isMarketHoursActive = (currentHour >= ActiveStartHour && currentHour < ActiveEndHour);
         break;
      case MARKET_HOURS_CUSTOM:
         // Custom logic can be added here
         isMarketHoursActive = true;
         break;
      default: // MARKET_HOURS_ALL
         isMarketHoursActive = true;
         break;
   }

   // Apply day filters
   if(MondayFilter && currentDay == 1) // Monday
      isMarketHoursActive = false;

   if(FridayFilter && currentDay == 5) // Friday
   {
      if(currentHour >= FridayEndHour)
         isMarketHoursActive = false;
   }

   // Update timestamp
   marketCondition.lastUpdate = TimeCurrent();

   if(DEBUG)
   {
      PrintFormat("Market Conditions Updated: Volatility=%.5f, Regime=%s, TrendUp=%s, StrongTrend=%s, Overbought=%s, Oversold=%s, HoursActive=%s",
                 marketCondition.volatility,
                 EnumToString(currentVolatilityRegime),
                 marketCondition.isTrendUp ? "true" : "false",
                 marketCondition.isStrongTrend ? "true" : "false",
                 marketCondition.isOverbought ? "true" : "false",
                 marketCondition.isOversold ? "true" : "false",
                 isMarketHoursActive ? "true" : "false");
   }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Clean up SystemInfo object
   if(sysInfo != NULL)
   {
      delete sysInfo;
      sysInfo = NULL;
   }

   // Release indicator handles
   ReleaseIndicatorHandles();

   // Free trailing positions array
   ArrayFree(trailingPositions);

   // Log performance metrics if available
   if(totalOrdersPlaced > 0 && (DEBUG || LogTradeDetails))
   {
      double successRate = (double)successfulOrders / totalOrdersPlaced * 100.0;
      PrintFormat("%s Performance: Orders=%d, Success=%.1f%%, Failed=%d, Modifications=%d",
                 OrderComment, totalOrdersPlaced, successRate, failedOrders, orderModifications);
   }

   if(DEBUG)
      PrintFormat("%s Deinitialized. Reason: %d", OrderComment, reason);
}

//+------------------------------------------------------------------+
//| Release all indicator handles                                    |
//+------------------------------------------------------------------+
void ReleaseIndicatorHandles()
{
   // Release ATR handle
   if(atrHandle != INVALID_HANDLE)
   {
      IndicatorRelease(atrHandle);
      atrHandle = INVALID_HANDLE;
   }

   // Release MA handles
   if(fastMAHandle != INVALID_HANDLE)
   {
      IndicatorRelease(fastMAHandle);
      fastMAHandle = INVALID_HANDLE;
   }

   if(slowMAHandle != INVALID_HANDLE)
   {
      IndicatorRelease(slowMAHandle);
      slowMAHandle = INVALID_HANDLE;
   }

   // Release ADX handle
   if(adxHandle != INVALID_HANDLE)
   {
      IndicatorRelease(adxHandle);
      adxHandle = INVALID_HANDLE;
   }

   // Release RSI handle
   if(rsiHandle != INVALID_HANDLE)
   {
      IndicatorRelease(rsiHandle);
      rsiHandle = INVALID_HANDLE;
   }

   // Release Stochastic handle
   if(stochHandle != INVALID_HANDLE)
   {
      IndicatorRelease(stochHandle);
      stochHandle = INVALID_HANDLE;
   }

   // Release Volume MA handle
   if(volumeMAHandle != INVALID_HANDLE)
   {
      IndicatorRelease(volumeMAHandle);
      volumeMAHandle = INVALID_HANDLE;
   }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Get latest market data
   if(!SymbolInfoTick(_Symbol, latestTick))
   {
      if(DEBUG) Print("SymbolInfoTick error in OnTick for ", _Symbol, ": ", GetLastError());
      return;
   }

   // Update symbol info
   symInfo.RefreshRates();

   // Check for emergency close conditions first (highest priority)
   if(EmergencyCloseEnabled)
      CheckEmergencyClose(latestTick);

   // Clean up closed positions from trailing tracking
   CleanupTrailingPositions();

   // Periodically update market conditions (every 10 ticks or if not initialized)
   static int tickCounter = 0;
   if(marketCondition.lastUpdate == 0 || tickCounter >= 10)
   {
      UpdateMarketConditions();
      tickCounter = 0;

      // Periodically report performance metrics
      if(DEBUG && totalOrdersPlaced > 0 && TimeCurrent() - lastPerformanceReport > 3600) // Every hour
      {
         double successRate = (double)successfulOrders / totalOrdersPlaced * 100.0;
         PrintFormat("Performance Update: Orders=%d, Success=%.1f%%, Failed=%d, Modifications=%d",
                    totalOrdersPlaced, successRate, failedOrders, orderModifications);
         lastPerformanceReport = TimeCurrent();
      }
   }
   tickCounter++;

   // Check if trading is allowed during current market hours
   if(!isMarketHoursActive)
   {
      if(DEBUG && TimeCurrent() % 300 == 0) // Log every 5 minutes to avoid spam
         Print("Trading disabled due to market hours restrictions");
      return;
   }

   // Handle state timeouts and transitions
   HandleStateManagement();

   // Process based on current state
   switch(StateManagement)
   {
      case STATE_MACHINE:
         ProcessStateMachine();
         break;

      case STATE_ADVANCED:
         ProcessAdvancedStateMachine();
         break;

      default: // STATE_SIMPLE
         ProcessSimpleState();
         break;
   }
}

//+------------------------------------------------------------------+
//| Handle state management timeouts and transitions                 |
//+------------------------------------------------------------------+
void HandleStateManagement()
{
   // Skip if using simple state management
   if(StateManagement == STATE_SIMPLE)
      return;

   // Handle state timeouts
   if(currentState != STATE_NORMAL)
   {
      // Calculate appropriate timeout based on state and settings
      int timeoutSeconds = MathMax(5, adaptiveCooldownSeconds);

      // Add extra time for error recovery state
      if(currentState == STATE_ERROR_RECOVERY)
         timeoutSeconds = MathMax(10, adaptiveCooldownSeconds * 2);

      // Check for timeout
      if(TimeCurrent() - stateChangeTime > timeoutSeconds)
      {
         if(DEBUG)
            Print("State timeout: Transitioning from ", EnumToString(currentState),
                 " to STATE_NORMAL after ", TimeCurrent() - stateChangeTime, " seconds");

         // Store previous state for reference
         previousState = currentState;
         currentState = STATE_NORMAL;
         lastStateTransition = TimeCurrent();

         // Reset error counter if we're leaving error recovery
         if(previousState == STATE_ERROR_RECOVERY)
            consecutiveErrors = 0;
      }
   }
}

//+------------------------------------------------------------------+
//| Process using simple state management                            |
//+------------------------------------------------------------------+
void ProcessSimpleState()
{
   // Simple state management - always perform all operations

   // Check for emergency close conditions first (highest priority)
   if(EmergencyCloseEnabled)
      CheckEmergencyClose(latestTick);

   CheckExistingOrders();
   ManageOpenPositions(latestTick);
   ManagePendingOrders(latestTick);
   TrailActivePositionSL();
}

//+------------------------------------------------------------------+
//| Process using state machine approach                             |
//+------------------------------------------------------------------+
void ProcessStateMachine()
{
   switch(currentState)
   {
      case STATE_NORMAL:
         // Normal processing

         // Check for emergency close conditions first (highest priority)
         if(EmergencyCloseEnabled)
            CheckEmergencyClose(latestTick);

         CheckExistingOrders();
         ManageOpenPositions(latestTick);
         ManagePendingOrders(latestTick);
         TrailActivePositionSL();
         break;

      case STATE_WAITING_FOR_BUY_CONFIRMATION:
         // Only check if the buy order exists, don't create new ones

         // Check for emergency close conditions first (highest priority)
         if(EmergencyCloseEnabled)
            CheckEmergencyClose(latestTick);

         CheckExistingOrders();
         ManageOpenPositions(latestTick);
         TrailActivePositionSL();

         // Only check buy order confirmation
         if(buyStopTicket != 0)
         {
            if(ord.Select((ulong)buyStopTicket) && IsOrderMarketPlaced())
            {
               if(DEBUG) Print("Buy Stop #", buyStopTicket, " confirmed - returning to normal state");
               previousState = currentState;
               currentState = STATE_NORMAL;
               lastStateTransition = TimeCurrent();
            }
         }
         else
         {
            // Order disappeared - return to normal state
            if(DEBUG) Print("Buy Stop ticket reset - returning to normal state");
            previousState = currentState;
            currentState = STATE_NORMAL;
            lastStateTransition = TimeCurrent();
         }
         break;

      case STATE_WAITING_FOR_SELL_CONFIRMATION:
         // Only check if the sell order exists, don't create new ones

         // Check for emergency close conditions first (highest priority)
         if(EmergencyCloseEnabled)
            CheckEmergencyClose(latestTick);

         CheckExistingOrders();
         ManageOpenPositions(latestTick);
         TrailActivePositionSL();

         // Only check sell order confirmation
         if(sellStopTicket != 0)
         {
            if(ord.Select((ulong)sellStopTicket) && IsOrderMarketPlaced())
            {
               if(DEBUG) Print("Sell Stop #", sellStopTicket, " confirmed - returning to normal state");
               previousState = currentState;
               currentState = STATE_NORMAL;
               lastStateTransition = TimeCurrent();
            }
         }
         else
         {
            // Order disappeared - return to normal state
            if(DEBUG) Print("Sell Stop ticket reset - returning to normal state");
            previousState = currentState;
            currentState = STATE_NORMAL;
            lastStateTransition = TimeCurrent();
         }
         break;

      case STATE_PROCESSING_TRANSACTION:
         // Don't do anything while processing a transaction
         // This state is managed by OnTradeTransaction
         break;

      case STATE_ERROR_RECOVERY:
         // In error recovery mode, only check existing orders and manage positions
         // Don't create new orders until recovery is complete

         // Check for emergency close conditions first (highest priority)
         if(EmergencyCloseEnabled)
            CheckEmergencyClose(latestTick);

         CheckExistingOrders();
         ManageOpenPositions(latestTick);
         TrailActivePositionSL();
         break;

      case STATE_MARKET_MONITORING:
         // Just monitor market conditions, don't place new orders

         // Check for emergency close conditions first (highest priority)
         if(EmergencyCloseEnabled)
            CheckEmergencyClose(latestTick);

         UpdateMarketConditions();
         CheckExistingOrders();
         TrailActivePositionSL();

         // If market conditions are favorable, return to normal state
         if(!marketCondition.isOverbought && !marketCondition.isOversold)
         {
            if(DEBUG) Print("Market conditions normalized - returning to normal state");
            previousState = currentState;
            currentState = STATE_NORMAL;
            lastStateTransition = TimeCurrent();
         }
         break;
   }
}

//+------------------------------------------------------------------+
//| Process using advanced state machine approach                    |
//+------------------------------------------------------------------+
void ProcessAdvancedStateMachine()
{
   // First handle basic state machine logic
   ProcessStateMachine();

   // Add advanced recovery and adaptation logic
   if(currentState == STATE_NORMAL)
   {
      // Check for extreme market conditions that might require monitoring
      if((marketCondition.isOverbought || marketCondition.isOversold) &&
         currentVolatilityRegime == VOLATILITY_HIGH)
      {
         if(DEBUG) Print("Extreme market conditions detected - switching to monitoring state");
         previousState = currentState;
         currentState = STATE_MARKET_MONITORING;
         stateChangeTime = TimeCurrent();
         lastStateTransition = TimeCurrent();
      }
   }
}

//+------------------------------------------------------------------+
//| Check and track existing orders                                  |
//+------------------------------------------------------------------+
void CheckExistingOrders()
{
   // Store previous values for logging and tracking purposes
   OrderInfo prevBuyStopInfo = buyStopInfo;
   OrderInfo prevSellStopInfo = sellStopInfo;

   // Reset tickets - we'll find them if they exist
   buyStopTicket = 0;
   sellStopTicket = 0;
   buyStopInfo.ticket = 0;
   sellStopInfo.ticket = 0;

   int totalOrders = OrdersTotal();
   if(totalOrders > 0)
   {
      // Count total orders for this symbol and magic number
      int totalEAOrders = 0;
      int otherOrderTypes = 0;

      for(int i = totalOrders - 1; i >= 0; i--)
      {
         if(ord.SelectByIndex(i))
         {
            // Check if this order belongs to our EA
            if(ord.Symbol() == _Symbol && ord.Magic() == MagicNumber)
            {
               totalEAOrders++;

               // Store the ticket based on order type
               if(ord.OrderType() == ORDER_TYPE_BUY_STOP)
               {
                  buyStopTicket = (long)ord.Ticket();
                  buyStopInfo.ticket = buyStopTicket;
                  buyStopInfo.creationTime = (datetime)ord.TimeSetup();
                  buyStopInfo.lastUpdateTime = TimeCurrent();
                  buyStopInfo.price = ord.PriceOpen();
                  buyStopInfo.stopLoss = ord.StopLoss();
                  buyStopInfo.takeProfit = ord.TakeProfit();
                  buyStopInfo.lastKnownState = ord.State();

                  // Preserve modification attempts counter
                  if(prevBuyStopInfo.ticket == buyStopTicket)
                     buyStopInfo.modificationAttempts = prevBuyStopInfo.modificationAttempts;
                  else
                     buyStopInfo.modificationAttempts = 0;

                  buyStopInfo.isBeingModified = false; // Reset modification flag

                  if(DEBUG)
                     PrintFormat("Found existing Buy Stop #%d at price %s, state: %s",
                                buyStopTicket, DoubleToString(ord.PriceOpen(), symbolDigits),
                                EnumToString(ord.State()));
               }
               else if(ord.OrderType() == ORDER_TYPE_SELL_STOP)
               {
                  sellStopTicket = (long)ord.Ticket();
                  sellStopInfo.ticket = sellStopTicket;
                  sellStopInfo.creationTime = (datetime)ord.TimeSetup();
                  sellStopInfo.lastUpdateTime = TimeCurrent();
                  sellStopInfo.price = ord.PriceOpen();
                  sellStopInfo.stopLoss = ord.StopLoss();
                  sellStopInfo.takeProfit = ord.TakeProfit();
                  sellStopInfo.lastKnownState = ord.State();

                  // Preserve modification attempts counter
                  if(prevSellStopInfo.ticket == sellStopTicket)
                     sellStopInfo.modificationAttempts = prevSellStopInfo.modificationAttempts;
                  else
                     sellStopInfo.modificationAttempts = 0;

                  sellStopInfo.isBeingModified = false; // Reset modification flag

                  if(DEBUG)
                     PrintFormat("Found existing Sell Stop #%d at price %s, state: %s",
                                sellStopTicket, DoubleToString(ord.PriceOpen(), symbolDigits),
                                EnumToString(ord.State()));
               }
               else
               {
                  // Count other order types (limit orders, etc.)
                  otherOrderTypes++;

                  if(DEBUG)
                     PrintFormat("Found other order type: #%d, Type: %s",
                                (long)ord.Ticket(), EnumToString(ord.OrderType()));
               }
            }
         }
         else if(DEBUG)
         {
            PrintFormat("CheckExistingOrders: Failed to select order at index %d", i);
         }
      }

      // Log if we found more orders than expected
      if(totalEAOrders > 2) // We expect at most 2 orders (1 buy stop + 1 sell stop)
      {
         // This is important enough to log even without DEBUG
         PrintFormat("WARNING: Found %d orders for %s with magic %d (expected max: 2)",
                    totalEAOrders, _Symbol, MagicNumber);

         // If using advanced state management, enter error recovery mode
         if(StateManagement == STATE_ADVANCED && currentState != STATE_ERROR_RECOVERY)
         {
            PrintFormat("Entering error recovery mode due to unexpected order count");
            previousState = currentState;
            currentState = STATE_ERROR_RECOVERY;
            stateChangeTime = TimeCurrent();
            lastStateTransition = TimeCurrent();
         }
      }

      // Log if we found other order types
      if(otherOrderTypes > 0 && DEBUG)
      {
         PrintFormat("Found %d unexpected order types for %s with magic %d",
                    otherOrderTypes, _Symbol, MagicNumber);
      }
   }

   // Verify order states and detect changes
   VerifyOrderStates(prevBuyStopInfo, buyStopInfo, "Buy Stop");
   VerifyOrderStates(prevSellStopInfo, sellStopInfo, "Sell Stop");
}

//+------------------------------------------------------------------+
//| Verify order states and detect changes                           |
//+------------------------------------------------------------------+
void VerifyOrderStates(const OrderInfo &prevInfo, const OrderInfo &currentInfo, const string &orderTypeDesc)
{
   // Skip if both previous and current tickets are 0 (no order)
   if(prevInfo.ticket == 0 && currentInfo.ticket == 0)
      return;

   // Detect order disappearance
   if(prevInfo.ticket != 0 && currentInfo.ticket == 0)
   {
      // Order disappeared - log the event
      string logMessage = orderTypeDesc + " #" + IntegerToString(prevInfo.ticket) +
                         " no longer found. Last state: " + EnumToString(prevInfo.lastKnownState) +
                         ", Created: " + TimeToString(prevInfo.creationTime) +
                         ", Age: " + IntegerToString(TimeCurrent() - prevInfo.creationTime) + " seconds";

      // Always log order disappearance for tracking
      if(LogTradeDetails || DEBUG)
         Print(logMessage);

      // If order was being modified, increment error counter
      if(prevInfo.isBeingModified && StateManagement != STATE_SIMPLE)
      {
         consecutiveErrors++;

         if(consecutiveErrors >= 3 && StateManagement == STATE_ADVANCED)
         {
            PrintFormat("Multiple consecutive errors detected (%d) - entering error recovery mode", consecutiveErrors);
            previousState = currentState;
            currentState = STATE_ERROR_RECOVERY;
            stateChangeTime = TimeCurrent();
            lastStateTransition = TimeCurrent();
         }
      }
   }

   // Detect new order creation
   if(prevInfo.ticket == 0 && currentInfo.ticket != 0)
   {
      if(LogTradeDetails || DEBUG)
         PrintFormat("New %s #%d created at price %s",
                    orderTypeDesc, currentInfo.ticket,
                    DoubleToString(currentInfo.price, symbolDigits));

      // Update performance metrics
      totalOrdersPlaced++;
      successfulOrders++;
   }

   // Detect order state changes
   if(prevInfo.ticket == currentInfo.ticket && prevInfo.ticket != 0 &&
      prevInfo.lastKnownState != currentInfo.lastKnownState)
   {
      if(DEBUG)
         PrintFormat("%s #%d state changed from %s to %s",
                    orderTypeDesc, currentInfo.ticket,
                    EnumToString(prevInfo.lastKnownState),
                    EnumToString(currentInfo.lastKnownState));
   }

   // Detect price changes (modifications)
   if(prevInfo.ticket == currentInfo.ticket && prevInfo.ticket != 0 &&
      prevInfo.price != currentInfo.price)
   {
      if(LogTradeDetails || DEBUG)
         PrintFormat("%s #%d price modified from %s to %s",
                    orderTypeDesc, currentInfo.ticket,
                    DoubleToString(prevInfo.price, symbolDigits),
                    DoubleToString(currentInfo.price, symbolDigits));

      // Update performance metrics
      orderModifications++;
   }
}

//+------------------------------------------------------------------+
//| Manage open positions (SL, TP, etc.)                             |
//+------------------------------------------------------------------+
void ManageOpenPositions(const MqlTick &tick)
{
   // Process positions that need initial stop loss
   int totalPositions = PositionsTotal();
   int eaPositions = 0;

   for(int i = totalPositions - 1; i >= 0; i--)
   {
      if(pos.SelectByIndex(i))
      {
         if(pos.Symbol() == _Symbol && pos.Magic() == MagicNumber)
         {
            eaPositions++;
            ulong pos_ticket = pos.Ticket();
            ENUM_POSITION_TYPE position_type = pos.PositionType();
            double currentSL = pos.StopLoss();
            double currentTP = pos.TakeProfit();

            // Check if initial SL needs to be set (e.g., if it failed during OnTradeTransaction)
            if(currentSL == 0.0)
            {
               // This is important enough to log even without DEBUG
               Print("Position #", pos_ticket, " has no SL. Setting initial SL.");

               // Use synchronous mode for this critical operation
               bool previousAsyncMode = (ExecutionMode == EXECUTION_MODE_ASYNC);
               if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
               {
                  trade.SetAsyncMode(false);
               }

               // Set initial stop loss with retry mechanism
               bool slSet = false;
               int retries = 0;

               while(!slSet && retries < MaxRetries)
               {
                  slSet = SetInitialStopLoss(pos_ticket, position_type, tick);

                  if(!slSet)
                  {
                     retries++;
                     if(DEBUG) Print("Retry ", retries, "/", MaxRetries, " setting initial SL for position #", pos_ticket);
                     Sleep(RetryDelayMs);
                  }
               }

               // Log if we couldn't set the SL after all retries
               if(!slSet)
               {
                  Print("WARNING: Failed to set initial SL for position #", pos_ticket, " after ", MaxRetries, " attempts");

                  // If using advanced state management, enter error recovery mode
                  if(StateManagement == STATE_ADVANCED)
                  {
                     consecutiveErrors++;

                     if(consecutiveErrors >= 3)
                     {
                        PrintFormat("Multiple consecutive errors detected (%d) - entering error recovery mode", consecutiveErrors);
                        previousState = currentState;
                        currentState = STATE_ERROR_RECOVERY;
                        stateChangeTime = TimeCurrent();
                        lastStateTransition = TimeCurrent();
                     }
                  }
               }
               else
               {
                  // Reset error counter on success
                  consecutiveErrors = 0;
               }

               // Restore previous async mode
               if(ExecutionMode == EXECUTION_MODE_ASYNC || ExecutionMode == EXECUTION_MODE_HYBRID)
               {
                  trade.SetAsyncMode(previousAsyncMode);
               }
            }

            // Check for positions with SL but no TP (if needed)
            // This is optional and depends on your strategy
            /*
            if(currentSL != 0.0 && currentTP == 0.0)
            {
               // Set take profit if needed
               // Implementation depends on your strategy
            }
            */
         }
      }
   }

   // Log position count if it changed
   static int lastPositionCount = 0;
   if(eaPositions != lastPositionCount && (LogTradeDetails || DEBUG))
   {
      PrintFormat("Position count changed: %d -> %d", lastPositionCount, eaPositions);
      lastPositionCount = eaPositions;
   }

   // Process trailing stops for all positions in a single pass
   TrailActivePositionSL();
}

//+------------------------------------------------------------------+
//| Set initial stop loss for a position with return status           |
//+------------------------------------------------------------------+
bool SetInitialStopLoss(ulong position_ticket, ENUM_POSITION_TYPE position_type, const MqlTick &tick_for_validation)
{
   if(!pos.SelectByTicket(position_ticket))
   {
      Print("SetInitialSL: Could not select position #", position_ticket);
      return false;
   }

   double openPrice = pos.PriceOpen();
   double slPrice = 0;
   double sl_offset_value = 0;
   double stopLevelDistance = sysInfo.GetMinStopDistance(); // Use SystemInfo for consistent calculation

   // Get current market prices
   double ask = tick_for_validation.ask;
   double bid = tick_for_validation.bid;

   // Calculate SL offset based on selected mode
   switch(InitialSLMode)
   {
      case SL_MODE_POINTS:
         sl_offset_value = InitialSLPoints * point;
         break;

      case SL_MODE_PERCENTAGE:
         sl_offset_value = openPrice * (InitialSLPercentage / 100.0);
         if(sl_offset_value < point) sl_offset_value = point; // Ensure minimum 1 point
         break;

      case SL_MODE_ATR:
         {
            double atrVal = GetAtrValue();
            if(atrHandle == INVALID_HANDLE || atrVal <= (point * 0.1))
               sl_offset_value = InitialSLPoints * point; // Fallback to points
            else
               sl_offset_value = atrVal * AtrMultiplier;
         }
         break;

      case SL_MODE_ADAPTIVE:
         // Use pre-calculated adaptive SL distance
         sl_offset_value = adaptiveSLDistance;
         if(sl_offset_value <= (point * 0.1)) // If not calculated yet
            sl_offset_value = InitialSLPoints * point; // Fallback to points
         break;

      default:
         sl_offset_value = InitialSLPoints * point; // Default to points
         break;
   }

   // Calculate initial SL price based on position type
   if(position_type == POSITION_TYPE_BUY)
   {
      // For buy positions, SL is below open price
      slPrice = sysInfo.NormalizePrice(openPrice - sl_offset_value);

      // Ensure SL is below open price
      if(slPrice >= openPrice)
         slPrice = sysInfo.NormalizePrice(openPrice - stopLevelDistance);

      // Ensure SL respects broker's stop level from current market
      double maxAllowedSL = sysInfo.NormalizePrice(bid - stopLevelDistance);
      if(slPrice > maxAllowedSL)
         slPrice = maxAllowedSL;
   }
   else // POSITION_TYPE_SELL
   {
      // For sell positions, SL is above open price
      slPrice = sysInfo.NormalizePrice(openPrice + sl_offset_value);

      // Ensure SL is above open price
      if(slPrice <= openPrice)
         slPrice = sysInfo.NormalizePrice(openPrice + stopLevelDistance);

      // Ensure SL respects broker's stop level from current market
      double minAllowedSL = sysInfo.NormalizePrice(ask + stopLevelDistance);
      if(slPrice < minAllowedSL)
         slPrice = minAllowedSL;
   }

   // Final validation
   bool validSL = (position_type == POSITION_TYPE_BUY && slPrice < openPrice && slPrice != 0.0) ||
                  (position_type == POSITION_TYPE_SELL && slPrice > openPrice && slPrice != 0.0);

   if(validSL)
   {
      // Set the stop loss
      if(trade.PositionModify(position_ticket, slPrice, pos.TakeProfit()))
      {
         // This is important enough to log even without DEBUG
         if(LogTradeDetails || DEBUG)
            Print("Set Initial SL for Pos #", position_ticket, " (", EnumToString(position_type),
                 ") to ", DoubleToString(slPrice, symbolDigits), ". Open: ",
                 DoubleToString(openPrice, symbolDigits));
         return true;
      }
      else
      {
         // One retry with fresh market data
         MqlTick fresh_tick;
         if(SymbolInfoTick(_Symbol, fresh_tick))
         {
            // Recalculate SL based on new market data
            if(position_type == POSITION_TYPE_BUY)
            {
               double maxAllowedSL = sysInfo.NormalizePrice(fresh_tick.bid - stopLevelDistance);
               if(slPrice > maxAllowedSL)
                  slPrice = maxAllowedSL;
            }
            else // SELL
            {
               double minAllowedSL = sysInfo.NormalizePrice(fresh_tick.ask + stopLevelDistance);
               if(slPrice < minAllowedSL)
                  slPrice = minAllowedSL;
            }

            // Try again with adjusted SL
            if(trade.PositionModify(position_ticket, slPrice, pos.TakeProfit()))
            {
               // This is important enough to log even without DEBUG
               if(LogTradeDetails || DEBUG)
                  Print("Set Initial SL for Pos #", position_ticket, " (", EnumToString(position_type),
                       ") to ", DoubleToString(slPrice, symbolDigits), " on retry");
               return true;
            }
            else
            {
               // This is important enough to log even without DEBUG
               Print("Failed to set Initial SL for Pos #", position_ticket, ": ",
                    trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
               return false;
            }
         }
         else
         {
            Print("Failed to get fresh tick data for SL retry");
            return false;
         }
      }
   }
   else
   {
      // This is important enough to log even without DEBUG
      Print("Invalid SL calculated for Pos #", position_ticket, ": ",
           DoubleToString(slPrice, symbolDigits), ", Open: ", DoubleToString(openPrice, symbolDigits));
      return false;
   }
}

//+------------------------------------------------------------------+
//| Checks and cancels pending orders based on market conditions      |
//+------------------------------------------------------------------+
void CheckAndCancelPendingOrders()
{
   if(!CancelOrdersOnExhaustion)
      return; // Feature is disabled

   // Check for extreme market conditions
   bool extremeVolatility = (currentVolatilityRegime == VOLATILITY_HIGH);

   // Check for upward exhaustion - cancel buy stop orders
   if((IsUpwardExhaustion() || (extremeVolatility && marketCondition.isOverbought)) && buyStopTicket != 0)
   {
      if(ord.Select((ulong)buyStopTicket))
      {
         // This is important enough to log even without DEBUG
         if(LogTradeDetails || DEBUG)
            Print("Upward exhaustion detected. Canceling Buy Stop #", buyStopTicket, " to avoid potential reversal.");

         // Use synchronous mode for this critical operation
         bool previousAsyncMode = (ExecutionMode == EXECUTION_MODE_ASYNC);
         if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
            trade.SetAsyncMode(false);

         if(trade.OrderDelete(buyStopTicket))
         {
            // This is important enough to log even without DEBUG
            if(LogTradeDetails || DEBUG)
               Print("Successfully canceled Buy Stop #", buyStopTicket);

            // Update order info
            buyStopInfo.lastKnownState = ORDER_STATE_CANCELED;
            buyStopInfo.lastUpdateTime = TimeCurrent();

            // Reset ticket after successful cancellation
            buyStopTicket = 0;
            buyStopInfo.ticket = 0;

            // Reset error counter on success
            consecutiveErrors = 0;
         }
         else
         {
            // This is important enough to log even without DEBUG
            Print("Failed to cancel Buy Stop #", buyStopTicket, ": ",
                 trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());

            // Increment error counter
            consecutiveErrors++;

            // If using advanced state management and too many errors, enter recovery mode
            if(consecutiveErrors >= 3 && StateManagement == STATE_ADVANCED)
            {
               PrintFormat("Multiple consecutive errors detected (%d) - entering error recovery mode", consecutiveErrors);
               previousState = currentState;
               currentState = STATE_ERROR_RECOVERY;
               stateChangeTime = TimeCurrent();
               lastStateTransition = TimeCurrent();
            }
         }

         // Restore previous async mode
         if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
            trade.SetAsyncMode(previousAsyncMode);
      }
   }

   // Check for downward exhaustion - cancel sell stop orders
   if((IsDownwardExhaustion() || (extremeVolatility && marketCondition.isOversold)) && sellStopTicket != 0)
   {
      if(ord.Select((ulong)sellStopTicket))
      {
         // This is important enough to log even without DEBUG
         if(LogTradeDetails || DEBUG)
            Print("Downward exhaustion detected. Canceling Sell Stop #", sellStopTicket, " to avoid potential reversal.");

         // Use synchronous mode for this critical operation
         bool previousAsyncMode = (ExecutionMode == EXECUTION_MODE_ASYNC);
         if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
            trade.SetAsyncMode(false);

         if(trade.OrderDelete(sellStopTicket))
         {
            // This is important enough to log even without DEBUG
            if(LogTradeDetails || DEBUG)
               Print("Successfully canceled Sell Stop #", sellStopTicket);

            // Update order info
            sellStopInfo.lastKnownState = ORDER_STATE_CANCELED;
            sellStopInfo.lastUpdateTime = TimeCurrent();

            // Reset ticket after successful cancellation
            sellStopTicket = 0;
            sellStopInfo.ticket = 0;

            // Reset error counter on success
            consecutiveErrors = 0;
         }
         else
         {
            // This is important enough to log even without DEBUG
            Print("Failed to cancel Sell Stop #", sellStopTicket, ": ",
                 trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());

            // Increment error counter
            consecutiveErrors++;

            // If using advanced state management and too many errors, enter recovery mode
            if(consecutiveErrors >= 3 && StateManagement == STATE_ADVANCED)
            {
               PrintFormat("Multiple consecutive errors detected (%d) - entering error recovery mode", consecutiveErrors);
               previousState = currentState;
               currentState = STATE_ERROR_RECOVERY;
               stateChangeTime = TimeCurrent();
               lastStateTransition = TimeCurrent();
            }
         }

         // Restore previous async mode
         if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
            trade.SetAsyncMode(previousAsyncMode);
      }
   }

   // Additional check for high volatility - consider canceling both orders
   if(extremeVolatility && currentVolatilityRegime == VOLATILITY_HIGH &&
      (buyStopTicket != 0 || sellStopTicket != 0) &&
      StateManagement == STATE_ADVANCED)
   {
      // In advanced state management, consider entering market monitoring state
      if(currentState == STATE_NORMAL)
      {
         if(DEBUG) Print("High volatility detected - switching to market monitoring state");
         previousState = currentState;
         currentState = STATE_MARKET_MONITORING;
         stateChangeTime = TimeCurrent();
         lastStateTransition = TimeCurrent();
      }
   }
}

//+------------------------------------------------------------------+
void ManagePendingOrders(const MqlTick &tick)
{
   // First check if we need to cancel any pending orders due to market exhaustion
   CheckAndCancelPendingOrders();

   // Manage Buy Stop order
   if(buyStopTicket != 0)
   {
      // Try to select the order by ticket
      if(!ord.Select((ulong)buyStopTicket))
      {
         Print("ManagePendingOrders: Could not select Buy Stop #", buyStopTicket, " - order may have been filled or canceled");
         buyStopTicket = 0; // Reset the ticket since we can't find the order
      }
      else if(!IsOrderMarketPlaced())
      {
         // Order exists but is not in a valid state
         Print("ManagePendingOrders: Buy Stop #", buyStopTicket, " exists but has invalid state: ",
               EnumToString(ord.State()));
         buyStopTicket = 0;
      }
      else
      {
         // Order exists and is valid - trail it
         TrailPendingBuyStop(tick);
      }
   }

   // Only place a new Buy Stop if we don't have one
   if(buyStopTicket == 0)
   {
      PlaceBuyStopOrder(tick);
   }

   // Manage Sell Stop order - same logic as above
   if(sellStopTicket != 0)
   {
      if(!ord.Select((ulong)sellStopTicket))
      {
         Print("ManagePendingOrders: Could not select Sell Stop #", sellStopTicket, " - order may have been filled or canceled");
         sellStopTicket = 0;
      }
      else if(!IsOrderMarketPlaced())
      {
         Print("ManagePendingOrders: Sell Stop #", sellStopTicket, " exists but has invalid state: ",
               EnumToString(ord.State()));
         sellStopTicket = 0;
      }
      else
      {
         TrailPendingSellStop(tick);
      }
   }

   // Only place a new Sell Stop if we don't have one
   if(sellStopTicket == 0)
   {
      PlaceSellStopOrder(tick);
   }
}

//+------------------------------------------------------------------+
bool IsOrderMarketPlaced()
{
    ENUM_ORDER_STATE state = ord.State();

    // Basic verification - just check the main valid states
    if(state == ORDER_STATE_PLACED || state == ORDER_STATE_STARTED || state == ORDER_STATE_PARTIAL)
        return true;

    // Enhanced verification - log unexpected states for debugging
    if(VerificationMode == VERIFICATION_ENHANCED && DEBUG)
    {
        Print("IsOrderMarketPlaced: Order #", ord.Ticket(), " has unexpected state: ",
              EnumToString(state));

        // Some states might be transitional but still valid
        if(state == ORDER_STATE_REQUEST_ADD || state == ORDER_STATE_REQUEST_MODIFY)
        {
            Print("IsOrderMarketPlaced: Order #", ord.Ticket(), " is in transitional state ",
                 EnumToString(state), " - considering valid");
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Place a buy stop order with enhanced entry criteria                |
//+------------------------------------------------------------------+
void PlaceBuyStopOrder(const MqlTick &tick)
{
   // Check if market is showing signs of upward exhaustion
   if(IsUpwardExhaustion())
   {
      if(DEBUG) Print("Upward exhaustion detected. Skipping Buy Stop placement to avoid potential reversal.");
      return;
   }

   // Check for extreme market conditions
   bool extremeVolatility = (currentVolatilityRegime == VOLATILITY_HIGH);
   if(extremeVolatility && marketCondition.isOverbought)
   {
      if(DEBUG) Print("High volatility with overbought conditions. Skipping Buy Stop order placement.");
      return;
   }

   // Check if market conditions are favorable for buy order
   if(!IsBuyConditionMet())
   {
      if(DEBUG) Print("Buy conditions not met based on trend filters. Skipping Buy Stop placement.");
      return;
   }

   // Double-check that we don't already have a buy stop order
   // This is an extra safety check to prevent duplicate orders
   if(buyStopTicket != 0)
   {
      if(DEBUG) Print("PlaceBuyStopOrder: Warning - Attempted to place Buy Stop when ticket #", buyStopTicket, " already exists");
      return;
   }

   // Check if we're in a state that should prevent order creation
   if((StateManagement == STATE_MACHINE || StateManagement == STATE_ADVANCED) &&
      (currentState == STATE_WAITING_FOR_BUY_CONFIRMATION ||
       currentState == STATE_WAITING_FOR_SELL_CONFIRMATION ||
       currentState == STATE_PROCESSING_TRANSACTION ||
       currentState == STATE_ERROR_RECOVERY ||
       currentState == STATE_MARKET_MONITORING))
   {
      if(DEBUG) Print("PlaceBuyStopOrder: Skipping order creation - EA is in ", EnumToString(currentState), " state");
      return;
   }

   // Check if we're in the cooldown period
   int effectiveCooldown = AdaptiveCooldown ? adaptiveCooldownSeconds : CooldownSeconds;
   if(TimeCurrent() - lastBuyOrderTime < effectiveCooldown)
   {
      if(DEBUG) Print("PlaceBuyStopOrder: Skipping order creation - in cooldown period (",
            effectiveCooldown - (TimeCurrent() - lastBuyOrderTime), " seconds remaining)");
      return;
   }

   // Check if we're processing a transaction
   if(isProcessingTransaction)
   {
      if(DEBUG) Print("PlaceBuyStopOrder: Skipping order creation - transaction in progress");
      return;
   }

   // Calculate order distance
   double orderDistance = 0.0;
   if(AdaptiveOrderDistance && adaptiveOrderDistance > 0)
   {
      // Use adaptive distance based on volatility
      orderDistance = adaptiveOrderDistance;

      if(DEBUG)
         Print("Using adaptive order distance: ", DoubleToString(orderDistance / point, 1), " points");
   }
   else
   {
      // Use fixed distance from input parameter
      orderDistance = OrderDistancePoints * point;
   }

   // Calculate order price
   double price = sysInfo.NormalizePrice(tick.ask + orderDistance);

   // Ensure order price respects broker's stop level
   double minPrice = sysInfo.NormalizePrice(tick.ask + sysInfo.GetMinStopDistance());
   if(price < minPrice)
   {
      if(DEBUG) Print("Adjusted Buy Stop price from ", DoubleToString(price, symbolDigits),
                     " to ", DoubleToString(minPrice, symbolDigits), " due to stop level");
      price = minPrice;
   }

   // Use synchronous mode for this critical operation
   bool previousAsyncMode = (ExecutionMode == EXECUTION_MODE_ASYNC);
   if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
      trade.SetAsyncMode(false);

   // Place the order
   if(DEBUG) Print("Attempting to place Buy Stop at ", DoubleToString(price, symbolDigits),
         " (Ask: ", DoubleToString(tick.ask, symbolDigits),
         "). Trend filters passed: MA=", IsMATrendUp() ? "UP" : "DOWN");

   if(trade.BuyStop(Lots, price, _Symbol, 0.0, 0.0, ORDER_TIME_GTC, 0, OrderComment))
   {
      buyStopTicket = (long)trade.ResultOrder();
      lastBuyOrderTime = TimeCurrent();

      // This is important enough to log even without DEBUG
      if(LogTradeDetails || DEBUG)
         Print("Successfully placed Buy Stop #", buyStopTicket, " at ", DoubleToString(price, symbolDigits));

      // Update order tracking info
      buyStopInfo.ticket = buyStopTicket;
      buyStopInfo.creationTime = TimeCurrent();
      buyStopInfo.lastUpdateTime = TimeCurrent();
      buyStopInfo.price = price;
      buyStopInfo.stopLoss = 0;
      buyStopInfo.takeProfit = 0;
      buyStopInfo.lastKnownState = ORDER_STATE_PLACED;
      buyStopInfo.modificationAttempts = 0;
      buyStopInfo.isBeingModified = false;

      // Update performance metrics
      totalOrdersPlaced++;
      successfulOrders++;

      // Reset error counter on success
      consecutiveErrors = 0;

      // Verify the order was actually placed with retries if using enhanced verification
      bool orderConfirmed = false;

      switch(VerificationMode)
      {
         case VERIFICATION_ENHANCED:
            if(buyStopTicket != 0)
            {
               int retries = 0;
               while(retries < MaxRetries && !orderConfirmed)
               {
                  if(ord.Select((ulong)buyStopTicket) && IsOrderMarketPlaced())
                  {
                     orderConfirmed = true;
                     buyStopInfo.lastKnownState = ord.State();
                     if(DEBUG) Print("Buy Stop #", buyStopTicket, " confirmed after ", retries, " retries with state: ",
                                    EnumToString(ord.State()));
                  }
                  else
                  {
                     retries++;
                     if(DEBUG) Print("Retry ", retries, "/", MaxRetries, ": Waiting for Buy Stop #", buyStopTicket, " confirmation...");
                     Sleep(RetryDelayMs); // Small delay between retries
                  }
               }

               if(!orderConfirmed)
               {
                  // This is important enough to log even without DEBUG
                  Print("Warning: Buy Stop #", buyStopTicket, " could not be confirmed after ", MaxRetries, " retries");
               }
            }
            break;

         case VERIFICATION_ADAPTIVE:
            // Adaptive verification based on market conditions
            if(buyStopTicket != 0)
            {
               int adaptiveRetries = MaxRetries;

               // Adjust retries based on volatility
               if(currentVolatilityRegime == VOLATILITY_HIGH)
                  adaptiveRetries = MaxRetries * 2;
               else if(currentVolatilityRegime == VOLATILITY_LOW)
                  adaptiveRetries = MathMax(1, MaxRetries / 2);

               int retries = 0;
               while(retries < adaptiveRetries && !orderConfirmed)
               {
                  if(ord.Select((ulong)buyStopTicket) && IsOrderMarketPlaced())
                  {
                     orderConfirmed = true;
                     buyStopInfo.lastKnownState = ord.State();
                     if(DEBUG) Print("Buy Stop #", buyStopTicket, " confirmed after ", retries, " retries with state: ",
                                    EnumToString(ord.State()));
                  }
                  else
                  {
                     retries++;
                     if(DEBUG) Print("Adaptive retry ", retries, "/", adaptiveRetries, ": Waiting for Buy Stop #", buyStopTicket, " confirmation...");
                     Sleep(RetryDelayMs); // Small delay between retries
                  }
               }

               if(!orderConfirmed)
               {
                  // This is important enough to log even without DEBUG
                  Print("Warning: Buy Stop #", buyStopTicket, " could not be confirmed after ", adaptiveRetries, " adaptive retries");
               }
            }
            break;

         default: // VERIFICATION_BASIC
            if(buyStopTicket != 0 && ord.Select((ulong)buyStopTicket))
            {
               // Basic verification
               orderConfirmed = IsOrderMarketPlaced();
               buyStopInfo.lastKnownState = ord.State();
               if(DEBUG) Print("Verified Buy Stop #", buyStopTicket, " exists with state: ",
                              EnumToString(ord.State()));
            }
            else if(DEBUG)
            {
               Print("Warning: Buy Stop order reported success but verification failed");
            }
            break;
      }

      // Update state if using state machine
      if((StateManagement == STATE_MACHINE || StateManagement == STATE_ADVANCED) && buyStopTicket != 0)
      {
         if(!orderConfirmed)
         {
            previousState = currentState;
            currentState = STATE_WAITING_FOR_BUY_CONFIRMATION;
            stateChangeTime = TimeCurrent();
            lastStateTransition = TimeCurrent();
            if(DEBUG) Print("Changing state to STATE_WAITING_FOR_BUY_CONFIRMATION for ticket #", buyStopTicket);
         }
      }
   }
   else
   {
      // This is important enough to log even without DEBUG
      Print("Error placing Buy Stop: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription(),
            ". Target: ", DoubleToString(price, symbolDigits),
            ", Ask: ", DoubleToString(tick.ask, symbolDigits),
            ", MinPrice: ", DoubleToString(minPrice, symbolDigits),
            " (StopLevel: ", stopLevelPoints, ")");

      // Update performance metrics
      totalOrdersPlaced++;
      failedOrders++;

      // Increment error counter
      consecutiveErrors++;

      // If using advanced state management and too many errors, enter recovery mode
      if(consecutiveErrors >= 3 && StateManagement == STATE_ADVANCED)
      {
         PrintFormat("Multiple consecutive errors detected (%d) - entering error recovery mode", consecutiveErrors);
         previousState = currentState;
         currentState = STATE_ERROR_RECOVERY;
         stateChangeTime = TimeCurrent();
         lastStateTransition = TimeCurrent();
      }
   }

   // Restore previous async mode
   if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
      trade.SetAsyncMode(previousAsyncMode);
}

//+------------------------------------------------------------------+
void PlaceSellStopOrder(const MqlTick &tick)
{
   // Check if market is showing signs of downward exhaustion
   if(IsDownwardExhaustion())
   {
      if(DEBUG) Print("Downward exhaustion detected. Skipping Sell Stop placement to avoid potential reversal.");
      return;
   }

   // Check if market conditions are favorable for sell order
   if(!IsSellConditionMet())
   {
      if(DEBUG) Print("Sell conditions not met based on trend filters. Skipping Sell Stop placement.");
      return;
   }

   // Double-check that we don't already have a sell stop order
   // This is an extra safety check to prevent duplicate orders
   if(sellStopTicket != 0)
   {
      if(DEBUG) Print("PlaceSellStopOrder: Warning - Attempted to place Sell Stop when ticket #", sellStopTicket, " already exists");
      return;
   }

   // Check if we're in a state that should prevent order creation
   if(StateManagement == STATE_MACHINE &&
      (currentState == STATE_WAITING_FOR_BUY_CONFIRMATION ||
       currentState == STATE_WAITING_FOR_SELL_CONFIRMATION ||
       currentState == STATE_PROCESSING_TRANSACTION))
   {
      if(DEBUG) Print("PlaceSellStopOrder: Skipping order creation - EA is in ", EnumToString(currentState), " state");
      return;
   }

   // Check if we're in the cooldown period
   if(TimeCurrent() - lastSellOrderTime < CooldownSeconds)
   {
      if(DEBUG) Print("PlaceSellStopOrder: Skipping order creation - in cooldown period (",
            CooldownSeconds - (TimeCurrent() - lastSellOrderTime), " seconds remaining)");
      return;
   }

   // Check if we're processing a transaction
   if(isProcessingTransaction)
   {
      if(DEBUG) Print("PlaceSellStopOrder: Skipping order creation - transaction in progress");
      return;
   }

   // Calculate the order price
   double price = NormalizeDouble(tick.bid - OrderDistancePoints * point, symbolDigits);
   double maxPrice = NormalizeDouble(tick.bid - (stopLevelPoints + 1) * point, symbolDigits); // +1 for buffer
   if (price > maxPrice) price = maxPrice;

   // Place the order
   if(DEBUG) Print("Attempting to place Sell Stop at ", DoubleToString(price, symbolDigits),
         " (Bid: ", DoubleToString(tick.bid, symbolDigits),
         "). Trend filters passed: MA=", IsMATrendUp() ? "UP" : "DOWN");

   if(trade.SellStop(Lots, price, _Symbol, 0.0, 0.0, ORDER_TIME_GTC, 0, OrderComment))
   {
      sellStopTicket = (long)trade.ResultOrder();
      lastSellOrderTime = TimeCurrent();

      // This is important enough to log even without DEBUG
      Print("Successfully placed Sell Stop #", sellStopTicket, " at ", DoubleToString(price, symbolDigits));

      // Update order tracking info
      sellStopInfo.ticket = sellStopTicket;
      sellStopInfo.creationTime = TimeCurrent();
      sellStopInfo.price = price;

      // Verify the order was actually placed with retries if using enhanced verification
      bool orderConfirmed = false;

      if(VerificationMode == VERIFICATION_ENHANCED && sellStopTicket != 0)
      {
         int retries = 0;
         while(retries < MaxRetries && !orderConfirmed)
         {
            if(ord.Select((ulong)sellStopTicket) && IsOrderMarketPlaced())
            {
               orderConfirmed = true;
               sellStopInfo.lastKnownState = ord.State();
               if(DEBUG) Print("Sell Stop #", sellStopTicket, " confirmed after ", retries, " retries with state: ",
                              EnumToString(ord.State()));
            }
            else
            {
               retries++;
               if(DEBUG) Print("Retry ", retries, "/", MaxRetries, ": Waiting for Sell Stop #", sellStopTicket, " confirmation...");
               Sleep(RetryDelayMs); // Small delay between retries
            }
         }

         if(!orderConfirmed)
         {
            // This is important enough to log even without DEBUG
            Print("Warning: Sell Stop #", sellStopTicket, " could not be confirmed after ", MaxRetries, " retries");
         }
      }
      else if(sellStopTicket != 0 && ord.Select((ulong)sellStopTicket))
      {
         // Basic verification
         orderConfirmed = IsOrderMarketPlaced();
         sellStopInfo.lastKnownState = ord.State();
         if(DEBUG) Print("Verified Sell Stop #", sellStopTicket, " exists with state: ",
                        EnumToString(ord.State()));
      }
      else if(DEBUG)
      {
         Print("Warning: Sell Stop order reported success but verification failed");
      }

      // Update state if using state machine
      if(StateManagement == STATE_MACHINE && sellStopTicket != 0)
      {
         if(!orderConfirmed)
         {
            currentState = STATE_WAITING_FOR_SELL_CONFIRMATION;
            stateChangeTime = TimeCurrent();
            if(DEBUG) Print("Changing state to STATE_WAITING_FOR_SELL_CONFIRMATION for ticket #", sellStopTicket);
         }
      }
   }
   else
   {
      // This is important enough to log even without DEBUG
      Print("Error placing Sell Stop: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription(),
            ". Target: ", DoubleToString(price, symbolDigits),
            ", Bid: ", DoubleToString(tick.bid, symbolDigits),
            ", MaxPrice: ", DoubleToString(maxPrice, symbolDigits),
            " (StopLevel: ", stopLevelPoints, ")");
   }
}

//+------------------------------------------------------------------+
void TrailPendingBuyStop(const MqlTick &tick)
{
   double currentOrderPrice = ord.PriceOpen();
   double desiredPrice = NormalizeDouble(tick.ask + OrderDistancePoints * point, symbolDigits);
   double minAllowedPrice = NormalizeDouble(tick.ask + (stopLevelPoints + 1) * point, symbolDigits);
   if (desiredPrice < minAllowedPrice) desiredPrice = minAllowedPrice;

   if(desiredPrice < currentOrderPrice && MathAbs(desiredPrice - currentOrderPrice) > (point * 0.5)) // Only move "down" towards current price
   {
      if(trade.OrderModify(buyStopTicket, desiredPrice, 0.0, 0.0, ORDER_TIME_GTC, 0))
      {
         if(DEBUG) Print("Modified Buy Stop #", buyStopTicket, " from ",
                        DoubleToString(currentOrderPrice, symbolDigits), " to ",
                        DoubleToString(desiredPrice, symbolDigits));
      }
      else
      {
         // This is important enough to log even without DEBUG
         Print("Error modifying Buy Stop #", buyStopTicket, " (Trail): ",
               trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription(),
               ". CurP: ", DoubleToString(currentOrderPrice, symbolDigits),
               ", NewP: ", DoubleToString(desiredPrice, symbolDigits));
      }
   }
}

//+------------------------------------------------------------------+
void TrailPendingSellStop(const MqlTick &tick)
{
   double currentOrderPrice = ord.PriceOpen();
   double desiredPrice = NormalizeDouble(tick.bid - OrderDistancePoints * point, symbolDigits);
   double maxAllowedPrice = NormalizeDouble(tick.bid - (stopLevelPoints + 1) * point, symbolDigits);
   if (desiredPrice > maxAllowedPrice) desiredPrice = maxAllowedPrice;

   if(desiredPrice > currentOrderPrice && MathAbs(desiredPrice - currentOrderPrice) > (point * 0.5)) // Only move "up" towards current price
   {
      if(trade.OrderModify(sellStopTicket, desiredPrice, 0.0, 0.0, ORDER_TIME_GTC, 0))
      {
         if(DEBUG) Print("Modified Sell Stop #", sellStopTicket, " from ",
                        DoubleToString(currentOrderPrice, symbolDigits), " to ",
                        DoubleToString(desiredPrice, symbolDigits));
      }
      else
      {
         // This is important enough to log even without DEBUG
         Print("Error modifying Sell Stop #", sellStopTicket, " (Trail): ",
               trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription(),
               ". CurP: ", DoubleToString(currentOrderPrice, symbolDigits),
               ", NewP: ", DoubleToString(desiredPrice, symbolDigits));
      }
   }
}



//+------------------------------------------------------------------+
double GetAtrValue()
{
   if(atrHandle == INVALID_HANDLE)
   {
       if(DEBUG) Print("GetAtrValue: ATR handle is invalid. Cannot get ATR.");
       return 0.0;
   }
   double atr_buffer[1];
   // Copy the ATR value of the last completed bar (index 1)
   if(CopyBuffer(atrHandle, 0, 1, 1, atr_buffer) == 1)
   {
      return NormalizeDouble(atr_buffer[0], symbolDigits > 0 ? symbolDigits : 5); // Ensure precision for ATR
   }
   if(DEBUG) Print("Error copying ATR buffer for ", _Symbol, ": ", GetLastError(), ". Handle: ", atrHandle);
   return 0.0;
}

//+------------------------------------------------------------------+
// Checks for bearish price action patterns (potential top)
//+------------------------------------------------------------------+
bool IsBearishPriceActionPattern()
{
   if(!CheckPriceActionPatterns)
      return false;

   // Get price data
   MqlRates rates[];
   ArraySetAsSeries(rates, true);

   if(CopyRates(_Symbol, ExhaustionTimeframe, 0, PriceActionBars + 1, rates) <= 0)
      return false;

   // Check for bearish engulfing pattern
   bool bearishEngulfing = (rates[1].close > rates[1].open) && // Previous candle is bullish
                          (rates[0].open > rates[1].close) &&  // Current candle opens above previous close
                          (rates[0].close < rates[1].open) &&  // Current candle closes below previous open
                          (rates[0].close < rates[0].open);    // Current candle is bearish

   // Check for shooting star pattern
   bool shootingStar = (rates[1].close > rates[1].open) &&                      // Previous candle is bullish
                       (rates[0].high - rates[0].open > 2 * (rates[0].open - rates[0].close)) && // Long upper wick
                       ((rates[0].open - rates[0].close) > 0) &&                // Bearish candle
                       ((rates[0].close - rates[0].low) < (rates[0].open - rates[0].close) * 0.3); // Small or no lower wick

   // Check for evening star pattern (simplified)
   bool eveningStar = (PriceActionBars >= 3) &&
                      (rates[2].close > rates[2].open) && // First candle is bullish
                      (MathAbs(rates[1].close - rates[1].open) < (rates[2].high - rates[2].low) * 0.3) && // Second candle is small
                      (rates[0].close < rates[0].open) && // Third candle is bearish
                      (rates[0].close < (rates[2].open + rates[2].close) / 2); // Third candle closes below midpoint of first

   // Check for double top pattern (simplified)
   bool doubleTop = false;
   if(PriceActionBars >= 5)
   {
      double firstPeak = -1, secondPeak = -1;
      int firstPeakIdx = -1;

      // Find the highest high in the first half of the bars
      for(int i = PriceActionBars; i > PriceActionBars/2; i--)
      {
         if(firstPeak < rates[i].high)
         {
            firstPeak = rates[i].high;
            firstPeakIdx = i;
         }
      }

      // Find the highest high in the second half of the bars
      for(int i = PriceActionBars/2; i >= 0; i--)
      {
         if(secondPeak < rates[i].high)
            secondPeak = rates[i].high;
      }

      // Check if we have two similar peaks with a valley in between
      if(firstPeakIdx != -1 &&
         MathAbs(firstPeak - secondPeak) < (firstPeak * 0.003) && // Peaks within 0.3% of each other
         rates[firstPeakIdx/2].low < firstPeak * 0.997) // Valley at least 0.3% below peaks
      {
         doubleTop = true;
      }
   }

   return (bearishEngulfing || shootingStar || eveningStar || doubleTop);
}

//+------------------------------------------------------------------+
// Checks for bullish price action patterns (potential bottom)
//+------------------------------------------------------------------+
bool IsBullishPriceActionPattern()
{
   if(!CheckPriceActionPatterns)
      return false;

   // Get price data
   MqlRates rates[];
   ArraySetAsSeries(rates, true);

   if(CopyRates(_Symbol, ExhaustionTimeframe, 0, PriceActionBars + 1, rates) <= 0)
      return false;

   // Check for bullish engulfing pattern
   bool bullishEngulfing = (rates[1].close < rates[1].open) && // Previous candle is bearish
                          (rates[0].open < rates[1].close) &&  // Current candle opens below previous close
                          (rates[0].close > rates[1].open) &&  // Current candle closes above previous open
                          (rates[0].close > rates[0].open);    // Current candle is bullish

   // Check for hammer pattern
   bool hammer = (rates[1].close < rates[1].open) &&                      // Previous candle is bearish
                 (rates[0].low - rates[0].close > 2 * (rates[0].close - rates[0].open)) && // Long lower wick
                 ((rates[0].close - rates[0].open) > 0) &&                // Bullish candle
                 ((rates[0].high - rates[0].close) < (rates[0].close - rates[0].open) * 0.3); // Small or no upper wick

   // Check for morning star pattern (simplified)
   bool morningStar = (PriceActionBars >= 3) &&
                      (rates[2].close < rates[2].open) && // First candle is bearish
                      (MathAbs(rates[1].close - rates[1].open) < (rates[2].high - rates[2].low) * 0.3) && // Second candle is small
                      (rates[0].close > rates[0].open) && // Third candle is bullish
                      (rates[0].close > (rates[2].open + rates[2].close) / 2); // Third candle closes above midpoint of first

   // Check for double bottom pattern (simplified)
   bool doubleBottom = false;
   if(PriceActionBars >= 5)
   {
      double firstBottom = DBL_MAX, secondBottom = DBL_MAX;
      int firstBottomIdx = -1;

      // Find the lowest low in the first half of the bars
      for(int i = PriceActionBars; i > PriceActionBars/2; i--)
      {
         if(firstBottom > rates[i].low)
         {
            firstBottom = rates[i].low;
            firstBottomIdx = i;
         }
      }

      // Find the lowest low in the second half of the bars
      for(int i = PriceActionBars/2; i >= 0; i--)
      {
         if(secondBottom > rates[i].low)
            secondBottom = rates[i].low;
      }

      // Check if we have two similar bottoms with a peak in between
      if(firstBottomIdx != -1 &&
         MathAbs(firstBottom - secondBottom) < (firstBottom * 0.003) && // Bottoms within 0.3% of each other
         rates[firstBottomIdx/2].high > firstBottom * 1.003) // Peak at least 0.3% above bottoms
      {
         doubleBottom = true;
      }
   }

   return (bullishEngulfing || hammer || morningStar || doubleBottom);
}

//+------------------------------------------------------------------+
// Checks if market is showing signs of upward exhaustion (overbought)
//+------------------------------------------------------------------+
bool IsUpwardExhaustion()
{
   // Check for bearish price action patterns
   if(IsBearishPriceActionPattern())
      return true;

   // If exhaustion detection is disabled, always return false (no exhaustion)
   if(ExhaustionMode == EXHAUSTION_OFF)
      return false;

   bool rsiOverbought = false;
   bool stochOverbought = false;

   // Check RSI if enabled
   if(ExhaustionMode == EXHAUSTION_RSI || ExhaustionMode == EXHAUSTION_BOTH)
   {
      if(rsiHandle == INVALID_HANDLE)
         return false; // Default to false if indicator not available

      double rsiBuffer[];
      ArraySetAsSeries(rsiBuffer, true);

      int copied = CopyBuffer(rsiHandle, 0, 0, 2, rsiBuffer);

      if(copied <= 0)
         return false; // Default to false if data not available

      // Check if RSI is in overbought territory
      rsiOverbought = rsiBuffer[0] >= RSI_UpperThreshold;

      // If using only RSI, return its result
      if(ExhaustionMode == EXHAUSTION_RSI)
         return rsiOverbought;
   }

   // Check Stochastic if enabled
   if(ExhaustionMode == EXHAUSTION_STOCH || ExhaustionMode == EXHAUSTION_BOTH)
   {
      if(stochHandle == INVALID_HANDLE)
         return false; // Default to false if indicator not available

      double kBuffer[], dBuffer[];
      ArraySetAsSeries(kBuffer, true);
      ArraySetAsSeries(dBuffer, true);

      int copied1 = CopyBuffer(stochHandle, 0, 0, 2, kBuffer); // %K line
      int copied2 = CopyBuffer(stochHandle, 1, 0, 2, dBuffer); // %D line

      if(copied1 <= 0 || copied2 <= 0)
         return false; // Default to false if data not available

      // Check if both Stochastic lines are in overbought territory
      stochOverbought = (kBuffer[0] >= Stoch_UpperThreshold && dBuffer[0] >= Stoch_UpperThreshold);

      // If using only Stochastic, return its result
      if(ExhaustionMode == EXHAUSTION_STOCH)
         return stochOverbought;
   }

   // If both indicators are enabled, return true if either shows exhaustion
   return (rsiOverbought || stochOverbought);
}

//+------------------------------------------------------------------+
// Checks if market is showing signs of downward exhaustion (oversold)
//+------------------------------------------------------------------+
bool IsDownwardExhaustion()
{
   // Check for bullish price action patterns
   if(IsBullishPriceActionPattern())
      return true;

   // If exhaustion detection is disabled, always return false (no exhaustion)
   if(ExhaustionMode == EXHAUSTION_OFF)
      return false;

   bool rsiOversold = false;
   bool stochOversold = false;

   // Check RSI if enabled
   if(ExhaustionMode == EXHAUSTION_RSI || ExhaustionMode == EXHAUSTION_BOTH)
   {
      if(rsiHandle == INVALID_HANDLE)
         return false; // Default to false if indicator not available

      double rsiBuffer[];
      ArraySetAsSeries(rsiBuffer, true);

      int copied = CopyBuffer(rsiHandle, 0, 0, 2, rsiBuffer);

      if(copied <= 0)
         return false; // Default to false if data not available

      // Check if RSI is in oversold territory
      rsiOversold = rsiBuffer[0] <= RSI_LowerThreshold;

      // If using only RSI, return its result
      if(ExhaustionMode == EXHAUSTION_RSI)
         return rsiOversold;
   }

   // Check Stochastic if enabled
   if(ExhaustionMode == EXHAUSTION_STOCH || ExhaustionMode == EXHAUSTION_BOTH)
   {
      if(stochHandle == INVALID_HANDLE)
         return false; // Default to false if indicator not available

      double kBuffer[], dBuffer[];
      ArraySetAsSeries(kBuffer, true);
      ArraySetAsSeries(dBuffer, true);

      int copied1 = CopyBuffer(stochHandle, 0, 0, 2, kBuffer); // %K line
      int copied2 = CopyBuffer(stochHandle, 1, 0, 2, dBuffer); // %D line

      if(copied1 <= 0 || copied2 <= 0)
         return false; // Default to false if data not available

      // Check if both Stochastic lines are in oversold territory
      stochOversold = (kBuffer[0] <= Stoch_LowerThreshold && dBuffer[0] <= Stoch_LowerThreshold);

      // If using only Stochastic, return its result
      if(ExhaustionMode == EXHAUSTION_STOCH)
         return stochOversold;
   }

   // If both indicators are enabled, return true if either shows exhaustion
   return (rsiOversold || stochOversold);
}

//+------------------------------------------------------------------+
// Returns true if MA trend is up, false if down
//+------------------------------------------------------------------+
bool IsMATrendUp()
{
   if(fastMAHandle == INVALID_HANDLE || slowMAHandle == INVALID_HANDLE)
      return true; // Default to true if indicators not available

   double fastMABuffer[], slowMABuffer[];
   ArraySetAsSeries(fastMABuffer, true);
   ArraySetAsSeries(slowMABuffer, true);

   // Get current and previous values for confirmation
   int copied1 = CopyBuffer(fastMAHandle, 0, 0, 2, fastMABuffer);
   int copied2 = CopyBuffer(slowMAHandle, 0, 0, 2, slowMABuffer);

   if(copied1 <= 0 || copied2 <= 0)
      return true; // Default to true if data not available

   // Current values
   bool fastAboveSlow = fastMABuffer[0] > slowMABuffer[0];

   // Previous values (for confirmation)
   bool prevFastAboveSlow = fastMABuffer[1] > slowMABuffer[1];

   // Trend is up if fast MA is above slow MA and was also above in previous bar
   return fastAboveSlow && prevFastAboveSlow;
}

//+------------------------------------------------------------------+
// Returns true if ADX indicates strong trend
//+------------------------------------------------------------------+
bool IsADXTrendStrong(bool &isUp)
{
   if(adxHandle == INVALID_HANDLE)
      return true; // Default to true if indicator not available

   double adxBuffer[], plusDIBuffer[], minusDIBuffer[];
   ArraySetAsSeries(adxBuffer, true);
   ArraySetAsSeries(plusDIBuffer, true);
   ArraySetAsSeries(minusDIBuffer, true);

   int copied = CopyBuffer(adxHandle, 0, 0, 1, adxBuffer);       // Main ADX line
   int copied1 = CopyBuffer(adxHandle, 1, 0, 1, plusDIBuffer);   // +DI line
   int copied2 = CopyBuffer(adxHandle, 2, 0, 1, minusDIBuffer);  // -DI line

   if(copied <= 0 || copied1 <= 0 || copied2 <= 0)
      return true; // Default to true if data not available

   // Check if ADX is above threshold (strong trend)
   bool strongTrend = adxBuffer[0] >= ADX_Threshold;

   // Determine trend direction based on DI+ and DI-
   isUp = plusDIBuffer[0] > minusDIBuffer[0];

   return strongTrend;
}

//+------------------------------------------------------------------+
// Checks if market conditions are favorable for buy order
//+------------------------------------------------------------------+
bool IsBuyConditionMet()
{
   // If trend filtering is disabled, always return true
   if(TrendFilterMode == TREND_FILTER_OFF)
      return true;

   bool adxUp = false;
   bool adxStrong = false;
   bool maTrendUp = false;

   // Check MA trend if enabled
   if(TrendFilterMode == TREND_FILTER_MA || TrendFilterMode == TREND_FILTER_BOTH)
   {
      maTrendUp = IsMATrendUp();
      if(TrendFilterMode == TREND_FILTER_MA && !maTrendUp)
         return false; // MA trend is down, don't buy
   }

   // Check ADX trend if enabled
   if(TrendFilterMode == TREND_FILTER_ADX || TrendFilterMode == TREND_FILTER_BOTH)
   {
      adxStrong = IsADXTrendStrong(adxUp);
      if(TrendFilterMode == TREND_FILTER_ADX && (!adxStrong || !adxUp))
         return false; // ADX trend is not strong enough or is down
   }

   // If both filters are enabled, at least one must confirm uptrend
   if(TrendFilterMode == TREND_FILTER_BOTH)
   {
      if(!maTrendUp && (!adxStrong || !adxUp))
         return false; // Neither MA nor ADX confirms uptrend
   }

   return true;
}

//+------------------------------------------------------------------+
// Checks if market conditions are favorable for sell order
//+------------------------------------------------------------------+
bool IsSellConditionMet()
{
   // If trend filtering is disabled, always return true
   if(TrendFilterMode == TREND_FILTER_OFF)
      return true;

   bool adxUp = false;
   bool adxStrong = false;
   bool maTrendUp = false;

   // Check MA trend if enabled
   if(TrendFilterMode == TREND_FILTER_MA || TrendFilterMode == TREND_FILTER_BOTH)
   {
      maTrendUp = IsMATrendUp();
      if(TrendFilterMode == TREND_FILTER_MA && maTrendUp)
         return false; // MA trend is up, don't sell
   }

   // Check ADX trend if enabled
   if(TrendFilterMode == TREND_FILTER_ADX || TrendFilterMode == TREND_FILTER_BOTH)
   {
      adxStrong = IsADXTrendStrong(adxUp);
      if(TrendFilterMode == TREND_FILTER_ADX && (!adxStrong || adxUp))
         return false; // ADX trend is not strong enough or is up
   }

   // If both filters are enabled, at least one must confirm downtrend
   if(TrendFilterMode == TREND_FILTER_BOTH)
   {
      if(maTrendUp && (!adxStrong || adxUp))
         return false; // Neither MA nor ADX confirms downtrend
   }

   return true;
}

//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Trail stop loss for active positions                              |
//+------------------------------------------------------------------+
void TrailActivePositionSL()
{
   // Get current market prices once for all positions
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

   // Get minimum stop level distance
   double minTrailingDistance = sysInfo.GetMinStopDistance();

   // Process all positions
   int totalPositions = PositionsTotal();
   if(totalPositions <= 0)
      return;

   // Track if any positions were modified for performance metrics
   bool positionModified = false;

   // Use synchronous mode for all trailing operations
   bool previousAsyncMode = (ExecutionMode == EXECUTION_MODE_ASYNC);
   if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
      trade.SetAsyncMode(false);

   for(int i = totalPositions - 1; i >= 0; i--)
   {
      if(!pos.SelectByIndex(i))
         continue;

      // Skip positions that don't belong to this EA
      if(pos.Symbol() != _Symbol || pos.Magic() != MagicNumber)
         continue;

      ulong ticket = pos.Ticket();
      ENUM_POSITION_TYPE type = pos.PositionType();
      double openPrice = pos.PriceOpen();
      double currentSL = pos.StopLoss();
      double tp = pos.TakeProfit();
      double positionProfit = pos.Profit();

      // Skip positions without stop loss
      if(currentSL == 0.0)
         continue;

      // Calculate trailing stop offset based on selected mode
      double trail_offset_value = 0.0;

      switch(TrailingSLMode)
      {
         case SL_MODE_POINTS:
            trail_offset_value = TrailingStopLossActivePoints * point;
            break;

         case SL_MODE_PERCENTAGE:
            // Percentage for trailing is based on current price
            trail_offset_value = (type == POSITION_TYPE_BUY) ?
                                 bid * (TrailingSLPercentage / 100.0) :
                                 ask * (TrailingSLPercentage / 100.0);

            if(trail_offset_value < point)
               trail_offset_value = point; // Minimum 1 point offset
            break;

         case SL_MODE_ATR:
            {
               double atrVal = GetAtrValue();
               trail_offset_value = (atrVal > point * 0.1) ?
                                    atrVal * AtrMultiplier :
                                    TrailingStopLossActivePoints * point;
            }
            break;

         case SL_MODE_ADAPTIVE:
            trail_offset_value = (adaptiveSLDistance > point * 0.1) ?
                                 adaptiveSLDistance :
                                 TrailingStopLossActivePoints * point;
            break;

         default:
            trail_offset_value = TrailingStopLossActivePoints * point;
            break;
      }

      // Ensure minimum trailing distance
      if(trail_offset_value < minTrailingDistance)
         trail_offset_value = minTrailingDistance;

      // Process based on position type
      if(type == POSITION_TYPE_BUY)
      {
         // Check if position is in profit enough to trigger trailing
         double profitInPoints = (bid - openPrice) / point;
         double triggerPoints = (TrailingSLMode == SL_MODE_POINTS) ?
                               TrailingStopLossActivePoints :
                               (int)(trail_offset_value / point);

         // Only trail if we have enough profit
         if(profitInPoints <= triggerPoints)
            continue;

         // Mark this position as having reached trailing activation threshold
         int posIndex = FindTrailingPosition(ticket);
         bool wasActivatedBefore = (posIndex >= 0 && trailingPositions[posIndex].trailingActivated);

         // Update trailing activation status
         UpdateTrailingPosition(ticket, true, bid);

         double newSL = 0.0;

         // Check if this is the first time we're moving to breakeven
         bool isBreakevenMove = (currentSL <= openPrice && openPrice < bid - trail_offset_value);

         if(isBreakevenMove)
         {
            // Apply breakeven buffer if enabled
            newSL = UseBreakevenBuffer ?
                    sysInfo.NormalizePrice(openPrice + (BreakevenBuffer * point)) :
                    sysInfo.NormalizePrice(openPrice);

            // Only modify if new SL is better than current SL
            if(newSL > currentSL)
            {
               if(trade.PositionModify(ticket, newSL, tp))
               {
                  if(LogTradeDetails || DEBUG)
                     PrintFormat("Breakeven+: Buy #%d SL moved to %s (Entry%s). Current price: %s",
                                ticket, DoubleToString(newSL, symbolDigits),
                                UseBreakevenBuffer ? "+Buffer" : "",
                                DoubleToString(bid, symbolDigits));

                  positionModified = true;
                  consecutiveErrors = 0;
               }
            }
         }
         else
         {
            // Calculate new stop loss level
            newSL = sysInfo.NormalizePrice(bid - trail_offset_value);

            // Apply fast trailing exit if enabled and already in profit
            if(FastTrailingExit && currentSL > openPrice)
            {
               double fastTrailOffset = trail_offset_value - (FastTrailingPoints * point);
               if(fastTrailOffset >= minTrailingDistance)
               {
                  double fastSL = sysInfo.NormalizePrice(bid - fastTrailOffset);
                  if(fastSL > newSL)
                     newSL = fastSL;
               }
            }

            // Apply profit protection if enabled
            if(ProfitProtectionEnabled && wasActivatedBefore)
            {
               // Ensure minimum profit buffer is maintained
               double minProfitLevel = openPrice + (MinimumProfitBuffer * point);

               // If new SL would be below minimum profit level, adjust it
               if(newSL < minProfitLevel)
               {
                  double oldSL = newSL;
                  newSL = sysInfo.NormalizePrice(minProfitLevel);

                  if(DEBUG)
                     PrintFormat("Profit Protection: Buy #%d SL adjusted from %s to %s to maintain min profit",
                                ticket, DoubleToString(oldSL, symbolDigits),
                                DoubleToString(newSL, symbolDigits));
               }

               // Additional check: if current price would result in a loss, don't move SL down
               if(currentSL > openPrice && bid < currentSL)
               {
                  if(DEBUG)
                     PrintFormat("Profit Protection: Buy #%d keeping current SL at %s as price %s would result in loss",
                                ticket, DoubleToString(currentSL, symbolDigits),
                                DoubleToString(bid, symbolDigits));
                  continue; // Skip this position
               }
            }

            // Only modify if new SL is better than current SL and price moved enough
            if(newSL > currentSL)
            {
               double priceMovement = MathAbs(bid - currentSL - trail_offset_value);
               if(priceMovement >= TrailingStepPoints * point)
               {
                  if(trade.PositionModify(ticket, newSL, tp))
                  {
                     if(LogTradeDetails || DEBUG)
                        PrintFormat("Trailing SL: Buy #%d SL moved from %s to %s. Current price: %s",
                                   ticket, DoubleToString(currentSL, symbolDigits),
                                   DoubleToString(newSL, symbolDigits),
                                   DoubleToString(bid, symbolDigits));

                     positionModified = true;
                     consecutiveErrors = 0;
                  }
                  else if(DEBUG)
                  {
                     PrintFormat("Failed to trail SL for Buy #%d: %d - %s",
                                ticket, trade.ResultRetcode(), trade.ResultRetcodeDescription());
                     consecutiveErrors++;
                  }
               }
            }
         }
      }
      else if(type == POSITION_TYPE_SELL)
      {
         // Check if position is in profit enough to trigger trailing
         double profitInPoints = (openPrice - ask) / point;
         double triggerPoints = (TrailingSLMode == SL_MODE_POINTS) ?
                               TrailingStopLossActivePoints :
                               (int)(trail_offset_value / point);

         // Only trail if we have enough profit
         if(profitInPoints <= triggerPoints)
            continue;

         // Mark this position as having reached trailing activation threshold
         int posIndex = FindTrailingPosition(ticket);
         bool wasActivatedBefore = (posIndex >= 0 && trailingPositions[posIndex].trailingActivated);

         // Update trailing activation status
         UpdateTrailingPosition(ticket, true, ask);

         double newSL = 0.0;

         // Check if this is the first time we're moving to breakeven
         bool isBreakevenMove = (currentSL >= openPrice && openPrice > ask + trail_offset_value);

         if(isBreakevenMove)
         {
            // Apply breakeven buffer if enabled
            newSL = UseBreakevenBuffer ?
                    sysInfo.NormalizePrice(openPrice - (BreakevenBuffer * point)) :
                    sysInfo.NormalizePrice(openPrice);

            // Only modify if new SL is better than current SL
            if(newSL < currentSL)
            {
               if(trade.PositionModify(ticket, newSL, tp))
               {
                  if(LogTradeDetails || DEBUG)
                     PrintFormat("Breakeven+: Sell #%d SL moved to %s (Entry%s). Current price: %s",
                                ticket, DoubleToString(newSL, symbolDigits),
                                UseBreakevenBuffer ? "-Buffer" : "",
                                DoubleToString(ask, symbolDigits));

                  positionModified = true;
                  consecutiveErrors = 0;
               }
            }
         }
         else
         {
            // Calculate new stop loss level
            newSL = sysInfo.NormalizePrice(ask + trail_offset_value);

            // Apply fast trailing exit if enabled and already in profit
            if(FastTrailingExit && currentSL < openPrice)
            {
               double fastTrailOffset = trail_offset_value - (FastTrailingPoints * point);
               if(fastTrailOffset >= minTrailingDistance)
               {
                  double fastSL = sysInfo.NormalizePrice(ask + fastTrailOffset);
                  if(fastSL < newSL)
                     newSL = fastSL;
               }
            }

            // Apply profit protection if enabled
            if(ProfitProtectionEnabled && wasActivatedBefore)
            {
               // Ensure minimum profit buffer is maintained
               double minProfitLevel = openPrice - (MinimumProfitBuffer * point);

               // If new SL would be above minimum profit level, adjust it
               if(newSL > minProfitLevel)
               {
                  double oldSL = newSL;
                  newSL = sysInfo.NormalizePrice(minProfitLevel);

                  if(DEBUG)
                     PrintFormat("Profit Protection: Sell #%d SL adjusted from %s to %s to maintain min profit",
                                ticket, DoubleToString(oldSL, symbolDigits),
                                DoubleToString(newSL, symbolDigits));
               }

               // Additional check: if current price would result in a loss, don't move SL up
               if(currentSL < openPrice && ask > currentSL)
               {
                  if(DEBUG)
                     PrintFormat("Profit Protection: Sell #%d keeping current SL at %s as price %s would result in loss",
                                ticket, DoubleToString(currentSL, symbolDigits),
                                DoubleToString(ask, symbolDigits));
                  continue; // Skip this position
               }
            }

            // Only modify if new SL is better than current SL and price moved enough
            if(newSL < currentSL)
            {
               double priceMovement = MathAbs(ask - currentSL + trail_offset_value);
               if(priceMovement >= TrailingStepPoints * point)
               {
                  if(trade.PositionModify(ticket, newSL, tp))
                  {
                     if(LogTradeDetails || DEBUG)
                        PrintFormat("Trailing SL: Sell #%d SL moved from %s to %s. Current price: %s",
                                   ticket, DoubleToString(currentSL, symbolDigits),
                                   DoubleToString(newSL, symbolDigits),
                                   DoubleToString(ask, symbolDigits));

                     positionModified = true;
                     consecutiveErrors = 0;
                  }
                  else if(DEBUG)
                  {
                     PrintFormat("Failed to trail SL for Sell #%d: %d - %s",
                                ticket, trade.ResultRetcode(), trade.ResultRetcodeDescription());
                     consecutiveErrors++;
                  }
               }
            }
         }
      }
   }

   // Restore previous async mode
   if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
      trade.SetAsyncMode(previousAsyncMode);

   // Handle error recovery if needed
   if(consecutiveErrors >= 3 && StateManagement == STATE_ADVANCED && currentState != STATE_ERROR_RECOVERY)
   {
      PrintFormat("Multiple consecutive errors detected (%d) - entering error recovery mode", consecutiveErrors);
      previousState = currentState;
      currentState = STATE_ERROR_RECOVERY;
      stateChangeTime = TimeCurrent();
      lastStateTransition = TimeCurrent();
   }

   // Update performance metrics if any positions were modified
   if(positionModified)
      orderModifications++;
}

//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result)
{
   // Set the transaction processing flag to prevent concurrent operations
   isProcessingTransaction = true;

   // If using state machine, update the state
   if(StateManagement == STATE_MACHINE)
   {
      currentState = STATE_PROCESSING_TRANSACTION;
      stateChangeTime = TimeCurrent();
   }

   // Log transaction details for debugging
   if(DEBUG) Print("OnTradeTransaction: Type=", EnumToString(trans.type), ", Order=", trans.order,
                  ", Position=", trans.position, ", Deal=", trans.deal);

   // Simplified relevance check - assumes if magic number is involved, it's relevant.
   // Or if it's one of our pending orders.
   bool isRelevant = false;
   if (request.magic == MagicNumber) isRelevant = true;
   else if (trans.order != 0 && (trans.order == (ulong)buyStopTicket || trans.order == (ulong)sellStopTicket)) isRelevant = true;
   else if (trans.position != 0) { // Check if position belongs to this EA if it's not from a known order
       if(pos.SelectByTicket(trans.position)) {
           if(pos.Magic() == MagicNumber) isRelevant = true;
       }
   }
    // Fallback: if order has our magic number (can happen if not tracked by buyStopTicket/sellStopTicket)
   if (!isRelevant && trans.order != 0 && ord.Select((ulong)trans.order)) {
        if (ord.Magic() == MagicNumber) isRelevant = true;
   }

   if (!isRelevant)
   {
      // Reset flags before returning
      isProcessingTransaction = false;
      if(StateManagement == STATE_MACHINE) currentState = STATE_NORMAL;
      return;
   }

   switch(trans.type)
   {
      case TRADE_TRANSACTION_DEAL_ADD: // A deal was added (position opened or modified by deal)
         // Check if this deal corresponds to one of our pending stop orders being triggered
         if(trans.order_type == ORDER_TYPE_BUY_STOP && trans.order == (ulong)buyStopTicket)
         {
            // This is important enough to log even without DEBUG
            Print("OnTradeTransaction: Buy Stop #", buyStopTicket, " triggered. Deal #", trans.deal,
                  ", Pos #", trans.position, ". Setting Initial SL.");

            // Set initial stop loss for the new position
            if(!SetInitialStopLoss(trans.position, POSITION_TYPE_BUY, latestTick))
               Print("Failed to set initial SL for position #", trans.position);

            // Add position to trailing tracking (not activated yet)
            UpdateTrailingPosition(trans.position, false, 0);

            // Store the last state before resetting
            OrderInfo lastBuyInfo = buyStopInfo;

            // Reset ticket as order is filled
            buyStopTicket = 0;
            buyStopInfo.ticket = 0;

            if(DEBUG) Print("Buy Stop #", lastBuyInfo.ticket, " filled and reset. Last state: ",
                           EnumToString(lastBuyInfo.lastKnownState),
                           ", Created: ", TimeToString(lastBuyInfo.creationTime),
                           ", Age: ", TimeCurrent() - lastBuyInfo.creationTime, " seconds");
         }
         else if(trans.order_type == ORDER_TYPE_SELL_STOP && trans.order == (ulong)sellStopTicket)
         {
            // This is important enough to log even without DEBUG
            Print("OnTradeTransaction: Sell Stop #", sellStopTicket, " triggered. Deal #", trans.deal,
                  ", Pos #", trans.position, ". Setting Initial SL.");

            // Set initial stop loss for the new position
            if(!SetInitialStopLoss(trans.position, POSITION_TYPE_SELL, latestTick))
               Print("Failed to set initial SL for position #", trans.position);

            // Add position to trailing tracking (not activated yet)
            UpdateTrailingPosition(trans.position, false, 0);

            // Store the last state before resetting
            OrderInfo lastSellInfo = sellStopInfo;

            // Reset ticket
            sellStopTicket = 0;
            sellStopInfo.ticket = 0;

            if(DEBUG) Print("Sell Stop #", lastSellInfo.ticket, " filled and reset. Last state: ",
                           EnumToString(lastSellInfo.lastKnownState),
                           ", Created: ", TimeToString(lastSellInfo.creationTime),
                           ", Age: ", TimeCurrent() - lastSellInfo.creationTime, " seconds");
         }
         // Check for any other positions that might have been opened with our magic number
         else if(trans.position != 0)
         {
            if(pos.SelectByTicket(trans.position) && pos.Magic() == MagicNumber)
            {
               // If this is a new position and it doesn't have a stop loss
               if(pos.StopLoss() == 0.0)
               {
                  // This is important enough to log even without DEBUG
                  Print("New position #", trans.position, " (", EnumToString(pos.PositionType()),
                        ") detected without SL. Setting initial SL.");

                  // Set initial stop loss
                  if(!SetInitialStopLoss(trans.position, pos.PositionType(), latestTick))
                     Print("Failed to set initial SL for position #", trans.position);

                  // Add position to trailing tracking (not activated yet)
                  UpdateTrailingPosition(trans.position, false, 0);
               }
            }

            // Check if this is a position close
            if(trans.deal_type == DEAL_TYPE_SELL || trans.deal_type == DEAL_TYPE_BUY)
            {
               // If position was closed, remove it from tracking
               if(FindTrailingPosition(trans.position) >= 0)
               {
                  if(DEBUG)
                     Print("Position #", trans.position, " closed, removing from trailing tracking");
                  RemoveTrailingPosition(trans.position);
               }
            }
         }
         break;

      case TRADE_TRANSACTION_ORDER_DELETE: // Pending order deleted
         if(trans.order == (ulong)buyStopTicket)
         {
            // Only log if it wasn't deleted due to being filled (TRADE_RETCODE_DONE/DONE_PARTIAL means filled)
            if(result.retcode != TRADE_RETCODE_DONE && result.retcode != TRADE_RETCODE_DONE_PARTIAL && result.retcode != TRADE_RETCODE_NO_CHANGES)
            {
                 if(DEBUG) Print("OnTradeTransaction: Buy Stop #", buyStopTicket,
                                " deleted (reason other than fill). Result: ", result.retcode,
                                " - ", result.comment);
            }

            // Store the last state before resetting
            OrderInfo lastBuyInfo = buyStopInfo;

            // Reset ticket
            buyStopTicket = 0;
            buyStopInfo.ticket = 0;

            if(DEBUG) Print("Buy Stop #", lastBuyInfo.ticket, " deleted. Last state: ",
                           EnumToString(lastBuyInfo.lastKnownState),
                           ", Created: ", TimeToString(lastBuyInfo.creationTime),
                           ", Age: ", TimeCurrent() - lastBuyInfo.creationTime, " seconds");
         }
         else if(trans.order == (ulong)sellStopTicket)
         {
            if(result.retcode != TRADE_RETCODE_DONE && result.retcode != TRADE_RETCODE_DONE_PARTIAL && result.retcode != TRADE_RETCODE_NO_CHANGES)
            {
                PrintFormat("OnTradeTransaction: Sell Stop #%d deleted (reason other than fill). Result: %u - %s",
                            sellStopTicket, result.retcode, result.comment);
            }

            // Store the last state before resetting
            OrderInfo lastSellInfo = sellStopInfo;

            // Reset ticket
            sellStopTicket = 0;
            sellStopInfo.ticket = 0;

            PrintFormat("Sell Stop #%d deleted. Last state: %s, Created: %s, Age: %d seconds",
                       lastSellInfo.ticket, EnumToString(lastSellInfo.lastKnownState),
                       TimeToString(lastSellInfo.creationTime),
                       TimeCurrent() - lastSellInfo.creationTime);
         }
         break;
   }

   // Reset the transaction processing flag
   isProcessingTransaction = false;

   // If using state machine, return to normal state
   if(StateManagement == STATE_MACHINE)
   {
      currentState = STATE_NORMAL;
      PrintFormat("Transaction processing complete - returning to STATE_NORMAL");
   }
}
//+------------------------------------------------------------------+
//| Check for emergency close conditions and close positions if needed |
//+------------------------------------------------------------------+
void CheckEmergencyClose(const MqlTick &tick)
{
   // Skip if emergency close is disabled
   if(!EmergencyCloseEnabled)
      return;

   // Skip if we're in a state that should prevent emergency close
   if((StateManagement == STATE_MACHINE || StateManagement == STATE_ADVANCED) &&
      (currentState == STATE_PROCESSING_TRANSACTION || currentState == STATE_ERROR_RECOVERY))
      return;

   // Process all positions
   int totalPositions = PositionsTotal();
   if(totalPositions <= 0)
      return;

   // Store previous async mode to restore later
   bool previousAsyncMode = (ExecutionMode == EXECUTION_MODE_ASYNC);

   // Always use synchronous mode for emergency close operations
   if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
      trade.SetAsyncMode(false);

   // Store original slippage to restore later
   int originalSlippage = Slippage;

   // Get current market prices
   double ask = tick.ask;
   double bid = tick.bid;

   // Determine if we're in high volatility conditions
   bool isHighVolatility = (currentVolatilityRegime == VOLATILITY_HIGH);

   // Check for market freeze conditions
   bool isMarketFrozen = IsMarketFrozen();

   // Set appropriate slippage for emergency close
   int emergencySlippage = MaxSlippagePoints;
   if(AdaptiveEmergencySlippage && (isHighVolatility || isMarketFrozen))
   {
      // Use higher slippage during high volatility or market freeze
      emergencySlippage = MaxAdaptiveSlippagePoints;

      if(DEBUG)
      {
         if(isMarketFrozen)
            Print("Using adaptive emergency slippage: ", emergencySlippage, " points due to market freeze");
         else if(isHighVolatility)
            Print("Using adaptive emergency slippage: ", emergencySlippage, " points due to high volatility");
      }
   }

   // Set deviation for emergency close
   trade.SetDeviationInPoints(emergencySlippage);

   // Track if any emergency closes were performed
   bool emergencyClosePerformed = false;

   // BATCH PROCESSING APPROACH
   if(BatchEmergencyClose)
   {
      // First pass: identify all positions that need emergency closing
      EmergencyClosePosition positionsToClose[];
      int positionsCount = 0;

      // Scan all positions and identify those needing emergency close
      for(int i = 0; i < totalPositions; i++)
      {
         if(pos.SelectByIndex(i))
         {
            if(pos.Symbol() == _Symbol && pos.Magic() == MagicNumber)
            {
               ulong ticket = pos.Ticket();
               ENUM_POSITION_TYPE type = pos.PositionType();
               double openPrice = pos.PriceOpen();
               double currentSL = pos.StopLoss();
               double positionVolume = pos.Volume();
               double positionProfit = pos.Profit();

               // Skip positions without stop loss
               if(currentSL == 0.0)
                  continue;

               bool shouldEmergencyClose = false;
               double priceDistance = 0.0; // Distance from current price to SL

               // Check if price has crossed or is about to cross the stop loss level
               if(type == POSITION_TYPE_BUY)
               {
                  // For buy positions, check if bid price is at or below stop loss
                  // Add a small buffer to detect crossing early
                  double emergencyLevel = currentSL + (EmergencyCloseBuffer * point);
                  shouldEmergencyClose = (bid <= emergencyLevel);
                  priceDistance = bid - currentSL;
               }
               else if(type == POSITION_TYPE_SELL)
               {
                  // For sell positions, check if ask price is at or above stop loss
                  // Add a small buffer to detect crossing early
                  double emergencyLevel = currentSL - (EmergencyCloseBuffer * point);
                  shouldEmergencyClose = (ask >= emergencyLevel);
                  priceDistance = currentSL - ask;
               }

               // If position needs emergency close, add it to the array
               if(shouldEmergencyClose)
               {
                  // Resize array to add new position
                  ArrayResize(positionsToClose, positionsCount + 1);

                  // Fill position data
                  positionsToClose[positionsCount].ticket = ticket;
                  positionsToClose[positionsCount].type = type;
                  positionsToClose[positionsCount].volume = positionVolume;
                  positionsToClose[positionsCount].openPrice = openPrice;
                  positionsToClose[positionsCount].currentSL = currentSL;
                  positionsToClose[positionsCount].profit = positionProfit;
                  positionsToClose[positionsCount].priceDistance = priceDistance;
                  positionsToClose[positionsCount].needsClose = true;

                  // Log the emergency close candidate
                  string priceInfo = (type == POSITION_TYPE_BUY) ?
                                    "Bid=" + DoubleToString(bid, symbolDigits) + ", SL=" + DoubleToString(currentSL, symbolDigits) :
                                    "Ask=" + DoubleToString(ask, symbolDigits) + ", SL=" + DoubleToString(currentSL, symbolDigits);

                  string profitInfo = "Profit=" + DoubleToString(positionProfit, 2) +
                                     ", Distance=" + DoubleToString(priceDistance / point, 1) + " pts";

                  Print("EMERGENCY CLOSE CANDIDATE: Position #", ticket, " (", EnumToString(type), ") - ",
                        priceInfo, ", ", profitInfo);

                  positionsCount++;
               }
            }
         }
      }

      // If we have positions to close
      if(positionsCount > 0)
      {
         Print("Found ", positionsCount, " positions requiring emergency close. Processing batch...");

         // Sort positions by priority if needed
         if(PrioritizeByProfit && positionsCount > 1)
         {
            // Sort by profit (descending) to close most profitable positions first
            SortPositionsByProfit(positionsToClose, positionsCount);

            if(DEBUG)
            {
               Print("Positions sorted by profit (descending):");
               for(int i = 0; i < positionsCount; i++)
               {
                  Print("  #", i+1, ": Ticket=", positionsToClose[i].ticket,
                        ", Profit=", DoubleToString(positionsToClose[i].profit, 2));
               }
            }
         }

         // Process all positions in the sorted order
         for(int i = 0; i < positionsCount; i++)
         {
            // Skip positions that don't need closing anymore (might have been closed by broker)
            if(!positionsToClose[i].needsClose)
               continue;

            // Check if position still exists
            if(!PositionSelectByTicket(positionsToClose[i].ticket))
            {
               Print("Position #", positionsToClose[i].ticket, " no longer exists, skipping");
               continue;
            }

            // Log the emergency close attempt
            string priceInfo = (positionsToClose[i].type == POSITION_TYPE_BUY) ?
                              "Bid=" + DoubleToString(bid, symbolDigits) + ", SL=" + DoubleToString(positionsToClose[i].currentSL, symbolDigits) :
                              "Ask=" + DoubleToString(ask, symbolDigits) + ", SL=" + DoubleToString(positionsToClose[i].currentSL, symbolDigits);

            string profitInfo = "Profit=" + DoubleToString(positionsToClose[i].profit, 2) +
                               ", Distance=" + DoubleToString(positionsToClose[i].priceDistance / point, 1) + " pts";

            Print("EMERGENCY CLOSE ATTEMPT: Position #", positionsToClose[i].ticket, " (", EnumToString(positionsToClose[i].type), ") - ",
                  priceInfo, ", ", profitInfo);

            // Try to close the position
            bool closeSuccess = ClosePositionWithRetries(positionsToClose[i], ask, bid, emergencySlippage, isMarketFrozen);

            if(closeSuccess)
               emergencyClosePerformed = true;
         }
      }
   }
   else // SEQUENTIAL PROCESSING (ORIGINAL APPROACH)
   {
      // Check all positions sequentially
      for(int i = totalPositions - 1; i >= 0; i--)
      {
         if(pos.SelectByIndex(i))
         {
            if(pos.Symbol() == _Symbol && pos.Magic() == MagicNumber)
            {
               ulong ticket = pos.Ticket();
               ENUM_POSITION_TYPE type = pos.PositionType();
               double openPrice = pos.PriceOpen();
               double currentSL = pos.StopLoss();
               double positionVolume = pos.Volume();
               double positionProfit = pos.Profit();

               // Skip positions without stop loss
               if(currentSL == 0.0)
                  continue;

               bool shouldEmergencyClose = false;
               double priceDistance = 0.0; // Distance from current price to SL

               // Check if price has crossed or is about to cross the stop loss level
               if(type == POSITION_TYPE_BUY)
               {
                  // For buy positions, check if bid price is at or below stop loss
                  // Add a small buffer to detect crossing early
                  double emergencyLevel = currentSL + (EmergencyCloseBuffer * point);
                  shouldEmergencyClose = (bid <= emergencyLevel);
                  priceDistance = bid - currentSL;
               }
               else if(type == POSITION_TYPE_SELL)
               {
                  // For sell positions, check if ask price is at or above stop loss
                  // Add a small buffer to detect crossing early
                  double emergencyLevel = currentSL - (EmergencyCloseBuffer * point);
                  shouldEmergencyClose = (ask >= emergencyLevel);
                  priceDistance = currentSL - ask;
               }

               // Perform emergency close if needed
               if(shouldEmergencyClose)
               {
                  // Create position structure for closing
                  EmergencyClosePosition posToClose;
                  posToClose.ticket = ticket;
                  posToClose.type = type;
                  posToClose.volume = positionVolume;
                  posToClose.openPrice = openPrice;
                  posToClose.currentSL = currentSL;
                  posToClose.profit = positionProfit;
                  posToClose.priceDistance = priceDistance;
                  posToClose.needsClose = true;

                  // Log the emergency close attempt
                  string priceInfo = (type == POSITION_TYPE_BUY) ?
                                    "Bid=" + DoubleToString(bid, symbolDigits) + ", SL=" + DoubleToString(currentSL, symbolDigits) :
                                    "Ask=" + DoubleToString(ask, symbolDigits) + ", SL=" + DoubleToString(currentSL, symbolDigits);

                  string profitInfo = "Profit=" + DoubleToString(positionProfit, 2) +
                                     ", Distance=" + DoubleToString(priceDistance / point, 1) + " pts";

                  Print("EMERGENCY CLOSE ATTEMPT: Position #", ticket, " (", EnumToString(type), ") - ",
                        priceInfo, ", ", profitInfo);

                  // Try to close the position using the helper function
                  bool closeSuccess = ClosePositionWithRetries(posToClose, ask, bid, emergencySlippage, isMarketFrozen);

                  if(closeSuccess)
                     emergencyClosePerformed = true;
               }
            }
         }
      }
   }

   // Reset error counter if emergency close was successful
   if(emergencyClosePerformed)
      consecutiveErrors = 0;

   // Restore previous settings
   trade.SetDeviationInPoints(originalSlippage); // Restore original slippage setting

   // Restore previous async mode
   if(previousAsyncMode || ExecutionMode == EXECUTION_MODE_HYBRID)
      trade.SetAsyncMode(previousAsyncMode);
}
//+------------------------------------------------------------------+
//| Sort positions by profit (descending) for batch emergency close   |
//+------------------------------------------------------------------+
void SortPositionsByProfit(EmergencyClosePosition &positions[], int count)
{
   // Simple bubble sort by profit (descending)
   for(int i = 0; i < count - 1; i++)
   {
      for(int j = 0; j < count - i - 1; j++)
      {
         if(positions[j].profit < positions[j + 1].profit)
         {
            // Swap positions
            EmergencyClosePosition temp = positions[j];
            positions[j] = positions[j + 1];
            positions[j + 1] = temp;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Close a position with retries and alternative methods            |
//+------------------------------------------------------------------+
bool ClosePositionWithRetries(EmergencyClosePosition &posData, double ask, double bid, int emergencySlippage, bool isMarketFrozen)
{
   // Try primary close method with retries
   bool closeSuccess = false;
   int retryCount = 0;
   uint lastErrorCode = 0;
   string lastErrorDesc = "";

   // Determine maximum retries based on market conditions
   int maxRetries = EmergencyRetryCount;
   if(isMarketFrozen && DetectMarketFreeze)
   {
      maxRetries = FreezeMaxRetries; // Use higher retry count during market freeze
      Print("Market freeze detected - using extended retry count: ", maxRetries);
   }

   // Primary close method with retries
   while(!closeSuccess && retryCount < maxRetries)
   {
      // Refresh market data on retries
      if(retryCount > 0)
      {
         // Get fresh market data
         MqlTick freshTick;
         if(SymbolInfoTick(_Symbol, freshTick))
         {
            // Update prices
            ask = freshTick.ask;
            bid = freshTick.bid;

            // Check if market is still frozen
            bool stillFrozen = IsMarketFrozen();

            // Increase slippage on retries
            int adaptiveSlippage = emergencySlippage;

            if(AdaptiveEmergencySlippage)
            {
               // Base increase on retry count
               adaptiveSlippage += (retryCount * 5); // Increase by 5 points per retry

               // Add extra slippage if market is frozen
               if(stillFrozen)
                  adaptiveSlippage += 10; // Add extra 10 points during freeze

               // Cap at maximum
               adaptiveSlippage = MathMin(adaptiveSlippage, MaxAdaptiveSlippagePoints);
               trade.SetDeviationInPoints(adaptiveSlippage);

               if(DEBUG)
               {
                  string freezeStatus = stillFrozen ? " (market frozen)" : "";
                  Print("Retry #", retryCount, ": Increased slippage to ", adaptiveSlippage, " points", freezeStatus);
               }
            }
         }
      }

      // Check if position still exists before trying to close it
      if(!PositionSelectByTicket(posData.ticket))
      {
         // Position no longer exists, might have been closed by broker or another process
         Print("Position #", posData.ticket, " no longer exists (error 10036 avoided)");
         closeSuccess = true;
         break;
      }

      // Try to close the position
      if(trade.PositionClose(posData.ticket))
      {
         Print("EMERGENCY CLOSE SUCCESSFUL: Position #", posData.ticket, " closed at market price (attempt ", retryCount + 1, ")");
         closeSuccess = true;
         break;
      }
      else
      {
         lastErrorCode = trade.ResultRetcode();
         lastErrorDesc = trade.ResultRetcodeDescription();

         // Check for position already closed error
         if(lastErrorCode == 10036) // ERR_ORDER_CLOSED
         {
            Print("Position #", posData.ticket, " already closed (error 10036 handled)");
            closeSuccess = true;
            break;
         }

         if(DEBUG)
            Print("Emergency close retry #", retryCount + 1, " failed: ", lastErrorCode, " - ", lastErrorDesc);

         // Wait before retrying
         if(retryCount < maxRetries - 1) // Don't sleep after the last attempt
         {
            // Use longer delay during market freeze
            int delayMs = EmergencyRetryDelayMs;
            if(isMarketFrozen)
            {
               // Exponential backoff during freeze (100ms, 200ms, 400ms, etc.)
               delayMs = EmergencyRetryDelayMs * (int)MathPow(2, retryCount);
               delayMs = MathMin(delayMs, 1000); // Cap at 1 second

               if(DEBUG)
                  Print("Market freeze: Using extended delay of ", delayMs, " ms for retry #", retryCount + 1);
            }
            Sleep(delayMs);
         }
      }

      retryCount++;
   }

   // If primary method failed and alternative method is enabled, try it
   if(!closeSuccess && UseAlternativeCloseMethod)
   {
      Print("Primary emergency close failed after ", retryCount, " attempts. Trying alternative methods...");

      // Check if position still exists before trying alternative methods
      if(!PositionSelectByTicket(posData.ticket))
      {
         Print("Position #", posData.ticket, " no longer exists before alternative methods");
         return true;
      }

      // Method 1: Partial close with maximum volume
      if(trade.PositionClosePartial(posData.ticket, posData.volume))
      {
         Print("ALTERNATIVE EMERGENCY CLOSE SUCCESSFUL: Position #", posData.ticket, " closed using partial close method");
         closeSuccess = true;
      }
      else
      {
         lastErrorCode = trade.ResultRetcode();
         lastErrorDesc = trade.ResultRetcodeDescription();

         // Check for position already closed error
         if(lastErrorCode == 10036) // ERR_ORDER_CLOSED
         {
            Print("Position #", posData.ticket, " already closed during alternative method 1 (error 10036 handled)");
            return true;
         }

         Print("ALTERNATIVE METHOD 1 FAILED: ", lastErrorCode, " - ", lastErrorDesc);

         // Check if position still exists
         if(!PositionSelectByTicket(posData.ticket))
         {
            Print("Position #", posData.ticket, " no longer exists after alternative method 1");
            return true;
         }

         // Method 2: Modify SL to current price or better
         double emergencySL;
         double slippageBuffer = emergencySlippage * point;

         if(posData.type == POSITION_TYPE_BUY)
         {
            // For buy positions, set SL slightly above current bid to ensure execution
            emergencySL = bid + slippageBuffer;
         }
         else // POSITION_TYPE_SELL
         {
            // For sell positions, set SL slightly below current ask to ensure execution
            emergencySL = ask - slippageBuffer;
         }

         // Normalize the price
         emergencySL = sysInfo.NormalizePrice(emergencySL);

         if(trade.PositionModify(posData.ticket, emergencySL, PositionGetDouble(POSITION_TP)))
         {
            Print("EMERGENCY SL MODIFICATION SUCCESSFUL: Position #", posData.ticket,
                  " SL moved to ", DoubleToString(emergencySL, symbolDigits));

            // Consider this a partial success
            closeSuccess = true;
         }
         else
         {
            lastErrorCode = trade.ResultRetcode();
            lastErrorDesc = trade.ResultRetcodeDescription();

            // Check for position already closed error
            if(lastErrorCode == 10036) // ERR_ORDER_CLOSED
            {
               Print("Position #", posData.ticket, " already closed during alternative method 2 (error 10036 handled)");
               return true;
            }

            Print("ALTERNATIVE METHOD 2 FAILED: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());

            // Check if position still exists
            if(!PositionSelectByTicket(posData.ticket))
            {
               Print("Position #", posData.ticket, " no longer exists after alternative method 2");
               return true;
            }

            // Method 3: Try to close with increased slippage during market freeze
            if(isMarketFrozen)
            {
               // Use maximum possible slippage in frozen market
               int maxPossibleSlippage = MaxAdaptiveSlippagePoints * 2;
               trade.SetDeviationInPoints(maxPossibleSlippage);

               Print("FROZEN MARKET - Attempting emergency close with maximum slippage: ", maxPossibleSlippage, " points");

               if(trade.PositionClose(posData.ticket))
               {
                  Print("MAXIMUM SLIPPAGE EMERGENCY CLOSE SUCCESSFUL: Position #", posData.ticket,
                        " closed with slippage of ", maxPossibleSlippage, " points");
                  closeSuccess = true;
               }
               else
               {
                  lastErrorCode = trade.ResultRetcode();
                  lastErrorDesc = trade.ResultRetcodeDescription();

                  // Check for position already closed error
                  if(lastErrorCode == 10036) // ERR_ORDER_CLOSED
                  {
                     Print("Position #", posData.ticket, " already closed during alternative method 3 (error 10036 handled)");
                     return true;
                  }

                  Print("ALTERNATIVE METHOD 3 FAILED: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
               }
            }
         }
      }
   }

   // Log final outcome if all methods failed
   if(!closeSuccess)
   {
      Print("EMERGENCY CLOSE FAILED: Position #", posData.ticket, " - Error: ", lastErrorCode, " - ", lastErrorDesc);

      // Increment error counter
      consecutiveErrors++;

      // If using advanced state management and too many errors, enter recovery mode
      if(consecutiveErrors >= 3 && StateManagement == STATE_ADVANCED)
      {
         PrintFormat("Multiple consecutive errors detected (%d) - entering error recovery mode", consecutiveErrors);
         previousState = currentState;
         currentState = STATE_ERROR_RECOVERY;
         stateChangeTime = TimeCurrent();
         lastStateTransition = TimeCurrent();
      }
   }

   return closeSuccess;
}

//+------------------------------------------------------------------+
//| Detect if market is in a frozen state                             |
//+------------------------------------------------------------------+
bool IsMarketFrozen()
{
   if(!DetectMarketFreeze)
      return false;

   static datetime lastTickTime = 0;
   static double lastBid = 0.0;
   static double lastAsk = 0.0;

   // Get current market data
   MqlTick currentTick;
   if(!SymbolInfoTick(_Symbol, currentTick))
      return false; // Can't determine if frozen

   datetime currentTime = TimeCurrent();

   // If this is the first check, initialize values
   if(lastTickTime == 0)
   {
      lastTickTime = currentTime;
      lastBid = currentTick.bid;
      lastAsk = currentTick.ask;
      return false;
   }

   // Check if prices haven't changed for a significant time
   bool pricesUnchanged = (MathAbs(currentTick.bid - lastBid) < 0.0000001 &&
                          MathAbs(currentTick.ask - lastAsk) < 0.0000001);

   // Calculate time difference in milliseconds
   long timeDiffMs = (currentTime - lastTickTime) * 1000;

   // Update stored values
   lastTickTime = currentTime;
   lastBid = currentTick.bid;
   lastAsk = currentTick.ask;

   // Market is considered frozen if prices haven't changed for longer than the detection time
   bool isFrozen = (pricesUnchanged && timeDiffMs >= FreezeDetectionMs);

   if(isFrozen && DEBUG)
      Print("MARKET FREEZE DETECTED: No price change for ", timeDiffMs, " ms");

   return isFrozen;
}
//+------------------------------------------------------------------+