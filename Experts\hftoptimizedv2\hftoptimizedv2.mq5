//+------------------------------------------------------------------+
//|                                   HFT_DualStops_Enhanced.mq5 |
//|                                     Generated by AI Assistant    |
//|                                             Your Name Here       |
//+------------------------------------------------------------------+
#property copyright "Your Name Here"
#property link      "https://www.example.com"
#property version   "1.29" // Optimized for execution speed, entry/exit precision, and live trading robustness
#property strict

//+------------------------------------------------------------------+
//| HFT Expert Advisor v1.29 - Performance Optimizations             |
//+------------------------------------------------------------------+
// Version 1.29 Performance Optimizations:
//
// 1. Tick Processing and Data Freshness
//    - Ensured critical price data is always fresh for decision-making
//    - Implemented time-based update frequency for indicators and filters
//    - Added fresh tick retrieval before critical operations (SL calculation, order placement)
//    - Optimized non-critical calculations to reduce CPU usage
//
// 2. Indicator Calculation Stability
//    - Modified indicator calculations to use completed bars for stability
//    - Improved decision quality by using stable data for trend assessment
//    - Maintained reactivity for M1-specific momentum and safety checks
//    - Enhanced coordination between different timeframe indicators
//
// 3. Trade Execution Latency Mitigation
//    - Added fresh tick retrieval before order placement and modification
//    - Optimized order confirmation process
//    - Implemented maximum slippage control for all trade operations
//    - Added guidance on execution mode selection
//
// 4. Advanced Performance Optimizations
//    - Minimized object creation and string operations in critical paths
//    - Implemented static arrays with optimized resize logic
//    - Added pending order valid duration to prevent stale order execution
//    - Enhanced performance measurement and logging

#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>
#include <Trade/OrderInfo.mqh>

CTrade trade;
CPositionInfo pos;
COrderInfo ord;

//--- Enums for configuration
enum ENUM_SL_MODE // General SL Mode Enum
{
   SL_MODE_POINTS,    // SL based on fixed points
   SL_MODE_ATR,       // SL based on ATR (Primarily for Initial SL)
   SL_MODE_PERCENTAGE // SL based on percentage of price
};

// New enums for EA optimization
enum ENUM_TRADE_EXECUTION_MODE
{
   EXECUTION_MODE_ASYNC,    // Asynchronous (faster but less reliable)
   EXECUTION_MODE_SYNC      // Synchronous (slower but more reliable)
};

enum ENUM_ORDER_VERIFICATION
{
   VERIFICATION_BASIC,      // Basic verification (faster)
   VERIFICATION_ENHANCED    // Enhanced with retries (more reliable)
};

enum ENUM_STATE_MANAGEMENT
{
   STATE_SIMPLE,            // Simple state management
   STATE_MACHINE            // State machine approach (more reliable)
};

enum ENUM_TREND_FILTER
{
   TREND_FILTER_OFF,        // No trend filtering
   TREND_FILTER_MA,         // Moving Average trend filter
   TREND_FILTER_ADX,        // ADX trend filter
   TREND_FILTER_BOTH        // Both MA and ADX filters
};

enum ENUM_EXHAUSTION_DETECTION
{
   EXHAUSTION_OFF,          // No exhaustion detection
   EXHAUSTION_RSI,          // RSI-based exhaustion detection
   EXHAUSTION_STOCH,        // Stochastic-based exhaustion detection
   EXHAUSTION_BOTH          // Both RSI and Stochastic
};

//--- Input Parameters ---
input group "Order Placement & Volume"
// Ensure 'Lots' meets your broker's minimum and step volume for US30 (e.g., min 0.1)
input double Lots                         = 0.1;    // Trade Volume
input int    OrderDistancePoints          = 1500;   // Distance for pending orders from current price (in points)
input ulong  MagicNumber                  = 202403; // EA's Unique Magic Number
input int    Slippage                     = 100;    // Max allowed slippage for order execution (in points)
input string OrderComment                 = "HFT_DualStops_v1.26";

input group "Initial Stop Loss Configuration"
input ENUM_SL_MODE InitialSLMode          = SL_MODE_ATR;    // Mode for initial SL of newly opened positions
input int    InitialSLPoints              = 1000;           // Initial SL in points (if SL_MODE_POINTS)
input double InitialSLPercentage          = 0.5;            // Initial SL as % of open price (e.g., 0.5 for 0.5%) (if SL_MODE_PERCENTAGE)
input ENUM_TIMEFRAMES AtrTimeframe        = PERIOD_M5;      // Timeframe for ATR calculation
input int    AtrPeriod                    = 14;             // Period for ATR indicator
input double AtrMultiplier                = 1.5;            // Multiplier for ATR value to set SL (if SL_MODE_ATR)

input group "Trailing Stop Loss (For Active Positions)"
input ENUM_SL_MODE TrailingSLMode             = SL_MODE_POINTS; // Mode for trailing SL
input int    TrailingStopLossActivePoints = 500;            // Trailing SL distance for open positions (in points, if SL_MODE_POINTS)
input double TrailingSLPercentage         = 0.25;           // Trailing SL as % of current price (e.g., 0.25 for 0.25%) (if SL_MODE_PERCENTAGE)
input bool   UseBreakevenPlusTrail        = true;           // Use Breakeven Plus feature for trailing
input int    BreakevenPlusBufferPoints    = 500;            // Buffer points above/below entry at breakeven (for fees)
input int    BreakevenPlusTriggerPoints   = 750;            // Profit points to trigger Breakeven Plus

input group "Entry Filters"
input ENUM_TREND_FILTER TrendFilterMode = TREND_FILTER_BOTH; // Trend filter mode
input ENUM_TIMEFRAMES   TrendTimeframe = PERIOD_H1;          // Timeframe for trend analysis
input int    FastMA_Period = 20;                             // Fast MA period
input int    SlowMA_Period = 50;                             // Slow MA period
input ENUM_MA_METHOD MA_Method = MODE_EMA;                   // MA method
input int    ADX_Period = 14;                                // ADX period
input double ADX_Threshold = 25;                             // Minimum ADX value for trend strength

input group "Exhaustion Detection"
input ENUM_EXHAUSTION_DETECTION ExhaustionMode = EXHAUSTION_BOTH; // Exhaustion detection mode
input ENUM_TIMEFRAMES ExhaustionTimeframe = PERIOD_M15;           // Timeframe for exhaustion detection
input int    RSI_Period = 14;                                     // RSI period
input double RSI_UpperThreshold = 70;                             // RSI upper threshold (overbought)
input double RSI_LowerThreshold = 30;                             // RSI lower threshold (oversold)
input int    Stoch_KPeriod = 14;                                  // Stochastic %K period
input int    Stoch_DPeriod = 3;                                   // Stochastic %D period
input int    Stoch_Slowing = 3;                                   // Stochastic slowing
input double Stoch_UpperThreshold = 80;                           // Stochastic upper threshold (overbought)
input double Stoch_LowerThreshold = 20;                           // Stochastic lower threshold (oversold)
input bool   CancelOrdersOnExhaustion = true;                     // Cancel pending orders on exhaustion signals
input bool   CheckPriceActionPatterns = true;                     // Check for price action reversal patterns
input int    PriceActionBars = 5;                                 // Number of bars to check for price action patterns

input group "M1 Safety Filters"
input bool   EnableM1MomentumFilter    = true;   // Enable M1 Momentum/Velocity Filter
input int    M1MomentumCandlesToCheck  = 2;      // Number of M1 candles to check for momentum (1-3)
input double M1MomentumCandleSizeMult  = 1.8;    // Multiplier for average candle size to detect momentum
input int    M1AvgRangePeriod          = 10;     // Period for calculating average M1 candle range
input int    M1MomentumFilterCooldownBars = 3;   // Cooldown bars after momentum filter triggers

input group "Volatility Filter"
input bool   EnableVolatilityFilter    = true;   // Enable High Volatility Filter
input ENUM_TIMEFRAMES VolatilityFilterTimeframe = PERIOD_M1; // Timeframe for volatility calculation
input int    VolatilityATR_Period      = 10;     // Period for ATR calculation
input int    VolatilityATR_MAPeriod    = 50;     // Period for ATR moving average
input double VolatilityATR_ThresholdMult = 2.5;  // Multiplier for ATR threshold

input group "EA Optimization Settings"
// EXECUTION_MODE_ASYNC is generally preferred for HFT client-side responsiveness
// EXECUTION_MODE_SYNC provides more predictable state transitions at the cost of potential client-side delays
// If experiencing discrepancies between backtest and live trading, testing with EXECUTION_MODE_SYNC
// can help diagnose if asynchronicity is contributing to the issue
input ENUM_TRADE_EXECUTION_MODE ExecutionMode = EXECUTION_MODE_ASYNC; // Trade execution mode
input ENUM_ORDER_VERIFICATION   VerificationMode = VERIFICATION_ENHANCED; // Order verification strategy
input ENUM_STATE_MANAGEMENT     StateManagement = STATE_MACHINE; // State management approach
input int    CooldownSeconds    = 2;             // Cooldown period between order operations (seconds)
input int    MaxRetries         = 3;             // Maximum number of retries for order operations
input int    RetryDelayMs       = 100;           // Delay between retries (milliseconds)
input int    TickProcessingInterval = 1;         // Process only every Nth tick (1 = all ticks)
input int    MinimumSLUpdatePoints = 10;         // Minimum points difference to update SL
input int    MaxSlippagePoints  = 10;            // Maximum allowed slippage in points
input int    PendingOrderMaxAgeSeconds = 180;    // Maximum age for pending orders (seconds)
input bool   DEBUG              = false;         // Enable detailed debug logging

//--- Global Variables ---
long   buyStopTicket  = 0;
long   sellStopTicket = 0;
double point;
int    symbolDigits;  // Renamed to avoid conflicts with library variables
int    atrHandle = INVALID_HANDLE;
int    fastMAHandle = INVALID_HANDLE;
int    slowMAHandle = INVALID_HANDLE;
int    adxHandle = INVALID_HANDLE;
int    rsiHandle = INVALID_HANDLE;
int    stochHandle = INVALID_HANDLE;
int    g_volatility_atr_handle = INVALID_HANDLE;
int    m1ATRHandle = INVALID_HANDLE;
MqlTick latestTick;
int    stopLevelPoints;
double minVolume;
double volumeStep;
double maxVolume;

// Volatility filter variables
bool   g_isMarketTooVolatile = false;
double currentATR = 0.0;
double atrMA = 0.0;

// M1 Momentum filter variables
datetime lastBuyMomentumTime = 0;
datetime lastSellMomentumTime = 0;
datetime g_m1_momentum_buy_cooldown_until = 0;
datetime g_m1_momentum_sell_cooldown_until = 0;
double   avgM1CandleRange = 0.0;

// Performance optimization variables
int      tick_counter = 0;
ulong    last_performance_report_time = 0;
ulong    total_tick_processing_time = 0;
int      processed_ticks_count = 0;

// State management variables
enum ENUM_EA_STATE {
   STATE_NORMAL,
   STATE_WAITING_FOR_BUY_CONFIRMATION,
   STATE_WAITING_FOR_SELL_CONFIRMATION,
   STATE_PROCESSING_TRANSACTION
};
ENUM_EA_STATE currentState = STATE_NORMAL;
datetime stateChangeTime = 0;
datetime lastBuyOrderTime = 0;
datetime lastSellOrderTime = 0;
bool isProcessingTransaction = false;

// Order tracking structures
struct OrderInfo {
   long ticket;
   datetime creationTime;
   double price;
   ENUM_ORDER_STATE lastKnownState;
};
OrderInfo buyStopInfo;
OrderInfo sellStopInfo;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize trade object with appropriate settings
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetDeviationInPoints(Slippage);
   trade.SetTypeFillingBySymbol(_Symbol);

   // Set execution mode based on input parameter
   trade.SetAsyncMode(ExecutionMode == EXECUTION_MODE_ASYNC);

   // Initialize state variables
   currentState = STATE_NORMAL;
   stateChangeTime = 0;
   lastBuyOrderTime = 0;
   lastSellOrderTime = 0;
   isProcessingTransaction = false;

   // Initialize order tracking structures
   buyStopInfo.ticket = 0;
   buyStopInfo.creationTime = 0;
   buyStopInfo.price = 0;
   buyStopInfo.lastKnownState = ORDER_STATE_STARTED;

   sellStopInfo.ticket = 0;
   sellStopInfo.creationTime = 0;
   sellStopInfo.price = 0;
   sellStopInfo.lastKnownState = ORDER_STATE_STARTED;

   point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   symbolDigits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   stopLevelPoints = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
   minVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   volumeStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   maxVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

   if(Lots < minVolume)
   {
      PrintFormat("Input 'Lots' (%.2f) is less than min vol (%.2f) for %s. EA terminated.", Lots, minVolume, _Symbol);
      return(INIT_PARAMETERS_INCORRECT);
   }
   if(Lots > maxVolume && maxVolume > 0)
   {
      PrintFormat("Input 'Lots' (%.2f) is greater than max vol (%.2f) for %s. EA terminated.", Lots, maxVolume, _Symbol);
      return(INIT_PARAMETERS_INCORRECT);
   }
   double remainder = MathMod(Lots, volumeStep);
   if(remainder > volumeStep * 0.0001 && remainder < volumeStep * 0.9999)
   {
      int precision_lots = (volumeStep < 0.001 ? 8 : (volumeStep < 0.01 ? 2 : (volumeStep < 0.1 ? 1 : 0)));
      PrintFormat("Input 'Lots' (%.*f) is not a valid multiple of vol step (%.*f) for %s. EA terminated.",
                  precision_lots, Lots, precision_lots, volumeStep, _Symbol);
      return(INIT_PARAMETERS_INCORRECT);
   }

   if(InitialSLMode == SL_MODE_ATR)
   {
      atrHandle = iATR(_Symbol, AtrTimeframe, AtrPeriod);
      if(atrHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating ATR indicator (code: %d). EA may not function as expected with ATR SL.", GetLastError());
         // Allow continuation, SetInitialStopLoss has fallback
      }
   }

   // Initialize trend filter indicators if enabled
   if(TrendFilterMode == TREND_FILTER_MA || TrendFilterMode == TREND_FILTER_BOTH)
   {
      fastMAHandle = iMA(_Symbol, TrendTimeframe, FastMA_Period, 0, MA_Method, PRICE_CLOSE);
      slowMAHandle = iMA(_Symbol, TrendTimeframe, SlowMA_Period, 0, MA_Method, PRICE_CLOSE);

      if(fastMAHandle == INVALID_HANDLE || slowMAHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating MA indicators (code: %d). Trend filtering may not work properly.", GetLastError());
      }
   }

   if(TrendFilterMode == TREND_FILTER_ADX || TrendFilterMode == TREND_FILTER_BOTH)
   {
      adxHandle = iADX(_Symbol, TrendTimeframe, ADX_Period);

      if(adxHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating ADX indicator (code: %d). Trend filtering may not work properly.", GetLastError());
      }
   }

   // Initialize exhaustion detection indicators if enabled
   if(ExhaustionMode == EXHAUSTION_RSI || ExhaustionMode == EXHAUSTION_BOTH)
   {
      rsiHandle = iRSI(_Symbol, ExhaustionTimeframe, RSI_Period, PRICE_CLOSE);

      if(rsiHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating RSI indicator (code: %d). Exhaustion detection may not work properly.", GetLastError());
      }
   }

   if(ExhaustionMode == EXHAUSTION_STOCH || ExhaustionMode == EXHAUSTION_BOTH)
   {
      stochHandle = iStochastic(_Symbol, ExhaustionTimeframe, Stoch_KPeriod, Stoch_DPeriod, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);

      if(stochHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating Stochastic indicator (code: %d). Exhaustion detection may not work properly.", GetLastError());
      }
   }

   // Initialize volatility filter ATR indicator if enabled
   if(EnableVolatilityFilter)
   {
      g_volatility_atr_handle = iATR(_Symbol, VolatilityFilterTimeframe, VolatilityATR_Period);

      if(g_volatility_atr_handle == INVALID_HANDLE)
      {
         PrintFormat("Error creating Volatility ATR indicator (code: %d). Volatility filter may not work properly.", GetLastError());
      }
   }

   // Initialize M1 Momentum filter ATR indicator if enabled
   if(EnableM1MomentumFilter)
   {
      m1ATRHandle = iATR(_Symbol, PERIOD_M1, M1AvgRangePeriod);

      if(m1ATRHandle == INVALID_HANDLE)
      {
         PrintFormat("Error creating M1 ATR indicator (code: %d). M1 Momentum filter may not work properly.", GetLastError());
      }

      // Initialize average M1 candle range
      avgM1CandleRange = GetM1AverageCandleRange(M1AvgRangePeriod);

      if(DEBUG)
         PrintFormat("Initial M1 Average Candle Range: %f points", avgM1CandleRange / point);
   }

   int minOrderDistance = stopLevelPoints + 10; // Small buffer
   if(OrderDistancePoints <= minOrderDistance)
   {
      PrintFormat("OrderDistancePoints (%d) too small for %s. Min (StopLevel + buffer): %d. EA terminated.",
                  OrderDistancePoints, _Symbol, minOrderDistance);
      return(INIT_PARAMETERS_INCORRECT);
   }

   if(InitialSLMode == SL_MODE_POINTS && InitialSLPoints <= stopLevelPoints)
   {
      PrintFormat("InitialSLPoints (%d) too small. Min (StopLevel): %d. EA terminated.",
                  InitialSLPoints, stopLevelPoints);
      return(INIT_PARAMETERS_INCORRECT);
   }
   if(InitialSLMode == SL_MODE_PERCENTAGE && (InitialSLPercentage <= 0.001 || InitialSLPercentage > 50.0)) // Added small lower bound
   {
      PrintFormat("InitialSLPercentage (%.3f%%) is out of reasonable range (0.001%%-50%%). EA terminated.", InitialSLPercentage);
      return(INIT_PARAMETERS_INCORRECT);
   }

   if(TrailingSLMode == SL_MODE_POINTS && TrailingStopLossActivePoints <= stopLevelPoints)
   {
      PrintFormat("TrailingStopLossActivePoints (%d) too small. Min (StopLevel): %d. EA terminated.",
                  TrailingStopLossActivePoints, stopLevelPoints);
      return(INIT_PARAMETERS_INCORRECT);
   }
   if(TrailingSLMode == SL_MODE_PERCENTAGE && (TrailingSLPercentage <= 0.001 || TrailingSLPercentage > 50.0)) // Added small lower bound
   {
      PrintFormat("TrailingSLPercentage (%.3f%%) is out of reasonable range (0.001%%-50%%). EA terminated.", TrailingSLPercentage);
      return(INIT_PARAMETERS_INCORRECT);
   }


   CheckExistingOrders();
   if(!SymbolInfoTick(_Symbol, latestTick))
   {
       PrintFormat("Failed to get initial tick for %s.", _Symbol);
       // Potentially return INIT_FAILED if a first tick is absolutely necessary for some logic before OnTick
   }

   if(DEBUG)
   {
      PrintFormat("%s Initialized. Symbol: %s, Point: %f, Digits: %d, Magic: %d",
                 OrderComment, _Symbol, point, symbolDigits, MagicNumber);
      PrintFormat("Volume: Lots=%.2f, MinVol=%.2f, MaxVol=%.2f, VolStep=%.2f",
                 Lots, minVolume, maxVolume, volumeStep);

      PrintFormat("Initial SL Mode: %s", EnumToString(InitialSLMode));
      if(InitialSLMode == SL_MODE_ATR)
         PrintFormat("ATR Config: TF=%s, Period=%d, Multiplier=%.2f. Handle: %d",
                    EnumToString(AtrTimeframe), AtrPeriod, AtrMultiplier, atrHandle);
      else if (InitialSLMode == SL_MODE_POINTS)
         PrintFormat("Initial SL Points: %d", InitialSLPoints);
      else // SL_MODE_PERCENTAGE
         PrintFormat("Initial SL Percentage: %.3f%% of Open Price", InitialSLPercentage);

      PrintFormat("Trailing SL Mode: %s", EnumToString(TrailingSLMode));
      if (TrailingSLMode == SL_MODE_POINTS)
           PrintFormat("Trailing SL Points: %d pts", TrailingStopLossActivePoints);
      else // SL_MODE_PERCENTAGE
           PrintFormat("Trailing SL Percentage: %.3f%% of Current Price", TrailingSLPercentage);

      // Log Breakeven Plus settings
      PrintFormat("Breakeven Plus: Enabled=%s, Buffer=%d pts, Trigger=%d pts",
                 UseBreakevenPlusTrail ? "Yes" : "No", BreakevenPlusBufferPoints, BreakevenPlusTriggerPoints);

      PrintFormat("Distances: Order=%d pts, StopLevel=%d pts", OrderDistancePoints, stopLevelPoints);

      // Log M1 Momentum Filter settings
      PrintFormat("M1 Momentum Filter: Enabled=%s, Candles=%d, Multiplier=%.1f, Period=%d, Cooldown=%d",
                 EnableM1MomentumFilter ? "Yes" : "No", M1MomentumCandlesToCheck,
                 M1MomentumCandleSizeMult, M1AvgRangePeriod, M1MomentumFilterCooldownBars);

      // Log Volatility Filter settings
      PrintFormat("Volatility Filter: Enabled=%s, TF=%s, ATR Period=%d, MA Period=%d, Threshold=%.1f",
                 EnableVolatilityFilter ? "Yes" : "No", EnumToString(VolatilityFilterTimeframe),
                 VolatilityATR_Period, VolatilityATR_MAPeriod, VolatilityATR_ThresholdMult);

      // Log optimization settings
      PrintFormat("Optimization Settings: ExecutionMode=%s, VerificationMode=%s, StateManagement=%s",
                 EnumToString(ExecutionMode), EnumToString(VerificationMode), EnumToString(StateManagement));
      PrintFormat("Cooldown=%d sec, MaxRetries=%d, RetryDelay=%d ms",
                 CooldownSeconds, MaxRetries, RetryDelayMs);
      PrintFormat("Performance Settings: TickInterval=%d, MinSLUpdate=%d pts, MaxSlippage=%d pts, MaxOrderAge=%d sec",
                 TickProcessingInterval, MinimumSLUpdatePoints, MaxSlippagePoints, PendingOrderMaxAgeSeconds);
   }
   else
   {
      // Minimal initialization message
      Print(OrderComment, " initialized on ", _Symbol);
   }

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Release ATR handle
   if(atrHandle != INVALID_HANDLE)
   {
      IndicatorRelease(atrHandle);
      atrHandle = INVALID_HANDLE;
   }

   // Release MA handles
   if(fastMAHandle != INVALID_HANDLE)
   {
      IndicatorRelease(fastMAHandle);
      fastMAHandle = INVALID_HANDLE;
   }

   if(slowMAHandle != INVALID_HANDLE)
   {
      IndicatorRelease(slowMAHandle);
      slowMAHandle = INVALID_HANDLE;
   }

   // Release ADX handle
   if(adxHandle != INVALID_HANDLE)
   {
      IndicatorRelease(adxHandle);
      adxHandle = INVALID_HANDLE;
   }

   // Release RSI handle
   if(rsiHandle != INVALID_HANDLE)
   {
      IndicatorRelease(rsiHandle);
      rsiHandle = INVALID_HANDLE;
   }

   // Release Stochastic handle
   if(stochHandle != INVALID_HANDLE)
   {
      IndicatorRelease(stochHandle);
      stochHandle = INVALID_HANDLE;
   }

   // Release Volatility ATR handle
   if(g_volatility_atr_handle != INVALID_HANDLE)
   {
      IndicatorRelease(g_volatility_atr_handle);
      g_volatility_atr_handle = INVALID_HANDLE;
   }

   // Release M1 ATR handle
   if(m1ATRHandle != INVALID_HANDLE)
   {
      IndicatorRelease(m1ATRHandle);
      m1ATRHandle = INVALID_HANDLE;
   }

   if(DEBUG)
      PrintFormat("%s Deinitialized. Reason: %d", OrderComment, reason);
}

//+------------------------------------------------------------------+
void OnTick()
{
   // Start performance measurement
   ulong start_time = GetMicrosecondCount();

   // CRITICAL: Get the latest tick data as the very first operation
   // This ensures all decisions are based on the most current market prices
   if(!SymbolInfoTick(_Symbol, latestTick))
   {
      Print("SymbolInfoTick error in OnTick for ", _Symbol, ": ", GetLastError());
      return;
   }

   // Increment tick counter
   tick_counter++;

   // Get current time once for all time-based checks
   datetime current_time = TimeCurrent();

   // Process critical price-sensitive operations on every tick

   // Update volatility status if filter is enabled - this is critical for risk management
   if(EnableVolatilityFilter)
   {
      // Only update volatility status on bar close (time-based, not tick-based)
      static datetime last_volatility_update = 0;
      static datetime last_volatility_bar_time = 0;
      datetime current_bar_time = iTime(_Symbol, VolatilityFilterTimeframe, 0);

      // Update exactly once per new bar or if it's been more than 30 seconds since last update
      if(current_bar_time != last_volatility_bar_time ||
         current_time - last_volatility_update > 30) // Fallback if bar time doesn't change
      {
         UpdateVolatilityStatus();
         last_volatility_update = current_time;
         last_volatility_bar_time = current_bar_time;

         if(DEBUG && g_isMarketTooVolatile)
            Print("OnTick: Volatility status updated on new bar. Market is too volatile.");
      }

      // If high volatility is detected, we can skip further processing
      if(g_isMarketTooVolatile)
      {
         // Only log occasionally to avoid log spam
         if(tick_counter % 100 == 0 && DEBUG)
            Print("OnTick: High volatility detected. Skipping order management.");
         return;
      }
   }

   // Periodically update M1 average candle range - only on M1 bar close (time-based)
   static datetime lastM1RangeUpdate = 0;
   static datetime last_m1_bar_time = 0;
   datetime current_m1_bar_time = iTime(_Symbol, PERIOD_M1, 0);

   if(EnableM1MomentumFilter && current_m1_bar_time != last_m1_bar_time)
   {
      avgM1CandleRange = GetM1AverageCandleRange(M1AvgRangePeriod);
      lastM1RangeUpdate = current_time;
      last_m1_bar_time = current_m1_bar_time;

      if(DEBUG)
         PrintFormat("M1 Average Candle Range updated on new M1 bar: %.5f points", avgM1CandleRange / point);
   }

   // Apply tick throttling for non-critical operations
   if(TickProcessingInterval > 1 && tick_counter % TickProcessingInterval != 0)
   {
      // Skip non-critical processing on this tick
      // Still measure and record performance
      ulong execution_time = GetMicrosecondCount() - start_time;
      total_tick_processing_time += execution_time;
      processed_ticks_count++;

      // Log performance data periodically
      if(DEBUG && GetMicrosecondCount() - last_performance_report_time > 60000000) // Every 60 seconds
      {
         PrintFormat("Performance: Avg tick processing time: %.2f μs over %d ticks (throttled)",
                    (double)total_tick_processing_time / MathMax(1, processed_ticks_count),
                    processed_ticks_count);
         last_performance_report_time = GetMicrosecondCount();
         total_tick_processing_time = 0;
         processed_ticks_count = 0;
      }

      return;
   }

   // Handle state timeouts if using state machine approach
   if(StateManagement == STATE_MACHINE && currentState != STATE_NORMAL)
   {
      // Check for state timeout (5 seconds or CooldownSeconds, whichever is greater)
      int timeoutSeconds = MathMax(5, CooldownSeconds);
      if(TimeCurrent() - stateChangeTime > timeoutSeconds)
      {
         if(DEBUG) Print("State timeout: Resetting from ", EnumToString(currentState),
                        " to STATE_NORMAL after ", TimeCurrent() - stateChangeTime, " seconds");
         currentState = STATE_NORMAL;
      }
   }

   // Process based on current state if using state machine approach
   if(StateManagement == STATE_MACHINE)
   {
      switch(currentState)
      {
         case STATE_NORMAL:
            // Normal processing - measure each function's execution time
            {
               ulong check_orders_start = GetMicrosecondCount();
               CheckExistingOrders();
               ulong check_orders_time = GetMicrosecondCount() - check_orders_start;

               ulong manage_positions_start = GetMicrosecondCount();
               ManageOpenPositions(latestTick);
               ulong manage_positions_time = GetMicrosecondCount() - manage_positions_start;

               ulong manage_orders_start = GetMicrosecondCount();
               ManagePendingOrders(latestTick);
               ulong manage_orders_time = GetMicrosecondCount() - manage_orders_start;

               ulong trail_sl_start = GetMicrosecondCount();
               TrailActivePositionSL();
               ulong trail_sl_time = GetMicrosecondCount() - trail_sl_start;

               // Log performance data for critical functions if they take too long
               if(DEBUG && (check_orders_time > 10000 || manage_positions_time > 10000 ||
                          manage_orders_time > 10000 || trail_sl_time > 10000))
               {
                  PrintFormat("Performance warning: CheckExistingOrders=%d μs, ManageOpenPositions=%d μs, ManagePendingOrders=%d μs, TrailActivePositionSL=%d μs",
                             check_orders_time, manage_positions_time, manage_orders_time, trail_sl_time);
               }
            }
            break;

         case STATE_WAITING_FOR_BUY_CONFIRMATION:
            // Only check if the buy order exists, don't create new ones
            CheckExistingOrders();
            ManageOpenPositions(latestTick);
            // Still process trailing stops
            TrailActivePositionSL();

            // Only check buy order confirmation
            if(buyStopTicket != 0)
            {
               if(ord.Select((ulong)buyStopTicket) && IsOrderMarketPlaced())
               {
                  if(DEBUG) Print("Buy Stop #", buyStopTicket, " confirmed - returning to normal state");
                  currentState = STATE_NORMAL;
               }
            }
            else
            {
               // Order disappeared - return to normal state
               if(DEBUG) Print("Buy Stop ticket reset - returning to normal state");
               currentState = STATE_NORMAL;
            }
            break;

         case STATE_WAITING_FOR_SELL_CONFIRMATION:
            // Only check if the sell order exists, don't create new ones
            CheckExistingOrders();
            ManageOpenPositions(latestTick);
            // Still process trailing stops
            TrailActivePositionSL();

            // Only check sell order confirmation
            if(sellStopTicket != 0)
            {
               if(ord.Select((ulong)sellStopTicket) && IsOrderMarketPlaced())
               {
                  if(DEBUG) Print("Sell Stop #", sellStopTicket, " confirmed - returning to normal state");
                  currentState = STATE_NORMAL;
               }
            }
            else
            {
               // Order disappeared - return to normal state
               if(DEBUG) Print("Sell Stop ticket reset - returning to normal state");
               currentState = STATE_NORMAL;
            }
            break;

         case STATE_PROCESSING_TRANSACTION:
            // Don't do anything while processing a transaction
            // This state is managed by OnTradeTransaction
            break;
      }
   }
   else
   {
      // Simple state management - always perform all operations
      ulong check_orders_start = GetMicrosecondCount();
      CheckExistingOrders();
      ulong check_orders_time = GetMicrosecondCount() - check_orders_start;

      ulong manage_positions_start = GetMicrosecondCount();
      ManageOpenPositions(latestTick);
      ulong manage_positions_time = GetMicrosecondCount() - manage_positions_start;

      ulong manage_orders_start = GetMicrosecondCount();
      ManagePendingOrders(latestTick);
      ulong manage_orders_time = GetMicrosecondCount() - manage_orders_start;

      ulong trail_sl_start = GetMicrosecondCount();
      TrailActivePositionSL();
      ulong trail_sl_time = GetMicrosecondCount() - trail_sl_start;

      // Log performance data for critical functions if they take too long
      if(DEBUG && (check_orders_time > 10000 || manage_positions_time > 10000 ||
                 manage_orders_time > 10000 || trail_sl_time > 10000))
      {
         PrintFormat("Performance warning: CheckExistingOrders=%d μs, ManageOpenPositions=%d μs, ManagePendingOrders=%d μs, TrailActivePositionSL=%d μs",
                    check_orders_time, manage_positions_time, manage_orders_time, trail_sl_time);
      }
   }

   // Record overall tick processing time
   ulong execution_time = GetMicrosecondCount() - start_time;
   total_tick_processing_time += execution_time;
   processed_ticks_count++;

   // Log performance data periodically
   if(DEBUG && GetMicrosecondCount() - last_performance_report_time > 60000000) // Every 60 seconds
   {
      PrintFormat("Performance: Avg tick processing time: %.2f μs over %d ticks",
                 (double)total_tick_processing_time / MathMax(1, processed_ticks_count),
                 processed_ticks_count);
      last_performance_report_time = GetMicrosecondCount();
      total_tick_processing_time = 0;
      processed_ticks_count = 0;
   }
}

//+------------------------------------------------------------------+
void CheckExistingOrders()
{
   // Store previous values for logging purposes
   OrderInfo prevBuyStopInfo = buyStopInfo;
   OrderInfo prevSellStopInfo = sellStopInfo;

   // Reset tickets - we'll find them if they exist
   buyStopTicket = 0;
   sellStopTicket = 0;
   buyStopInfo.ticket = 0;
   sellStopInfo.ticket = 0;

   int totalOrders = OrdersTotal();
   if(totalOrders > 0)
   {
      // Count total orders for this symbol and magic number
      int totalEAOrders = 0;

      for(int i = totalOrders - 1; i >= 0; i--)
      {
         if(ord.SelectByIndex(i))
         {
            // Check if this order belongs to our EA
            if(ord.Symbol() == _Symbol && ord.Magic() == MagicNumber)
            {
               totalEAOrders++;

               // Store the ticket based on order type
               if(ord.OrderType() == ORDER_TYPE_BUY_STOP)
               {
                  buyStopTicket = (long)ord.Ticket();
                  buyStopInfo.ticket = buyStopTicket;
                  buyStopInfo.creationTime = (datetime)ord.TimeSetup();
                  buyStopInfo.price = ord.PriceOpen();
                  buyStopInfo.lastKnownState = ord.State();

                  if(DEBUG)
                     PrintFormat("Found existing Buy Stop #%d at price %s, state: %s",
                                buyStopTicket, DoubleToString(ord.PriceOpen(), symbolDigits),
                                EnumToString(ord.State()));
               }
               else if(ord.OrderType() == ORDER_TYPE_SELL_STOP)
               {
                  sellStopTicket = (long)ord.Ticket();
                  sellStopInfo.ticket = sellStopTicket;
                  sellStopInfo.creationTime = (datetime)ord.TimeSetup();
                  sellStopInfo.price = ord.PriceOpen();
                  sellStopInfo.lastKnownState = ord.State();

                  if(DEBUG)
                     PrintFormat("Found existing Sell Stop #%d at price %s, state: %s",
                                sellStopTicket, DoubleToString(ord.PriceOpen(), symbolDigits),
                                EnumToString(ord.State()));
               }
            }
         }
         else if(DEBUG)
         {
            PrintFormat("CheckExistingOrders: Failed to select order at index %d", i);
         }
      }

      // Log if we found more orders than expected
      if(totalEAOrders > 2) // We expect at most 2 orders (1 buy stop + 1 sell stop)
      {
         // This is important enough to log even without DEBUG
         PrintFormat("WARNING: Found %d orders for %s with magic %d (expected max: 2)",
                    totalEAOrders, _Symbol, MagicNumber);
      }
   }

   // Log changes in ticket status for debugging
   if(DEBUG)
   {
      if(prevBuyStopInfo.ticket != 0 && buyStopInfo.ticket == 0)
      {
         PrintFormat("Order state change: Buy Stop #%d no longer found. Last state: %s, Created: %s",
                    prevBuyStopInfo.ticket,
                    EnumToString(prevBuyStopInfo.lastKnownState),
                    TimeToString(prevBuyStopInfo.creationTime));
      }

      if(prevSellStopInfo.ticket != 0 && sellStopInfo.ticket == 0)
      {
         PrintFormat("Order state change: Sell Stop #%d no longer found. Last state: %s, Created: %s",
                    prevSellStopInfo.ticket,
                    EnumToString(prevSellStopInfo.lastKnownState),
                    TimeToString(prevSellStopInfo.creationTime));
      }
   }
}

//+------------------------------------------------------------------+
void ManageOpenPositions(const MqlTick &tick)
{
   // Process positions that need initial stop loss
   int totalPositions = PositionsTotal();

   for(int i = totalPositions - 1; i >= 0; i--)
   {
      if(pos.SelectByIndex(i))
      {
         if(pos.Symbol() == _Symbol && pos.Magic() == MagicNumber)
         {
            ulong pos_ticket = pos.Ticket();
            ENUM_POSITION_TYPE position_type = pos.PositionType();
            double currentSL = pos.StopLoss();

            // Check if initial SL needs to be set (e.g., if it failed during OnTradeTransaction)
            if(currentSL == 0.0)
            {
               // This is important enough to log even without DEBUG
               Print("Position #", pos_ticket, " has no SL. Setting initial SL.");

               // Use synchronous mode for this critical operation
               bool previousAsyncMode = (ExecutionMode == EXECUTION_MODE_ASYNC);
               if(previousAsyncMode) trade.SetAsyncMode(false);

               SetInitialStopLoss(pos_ticket, position_type, tick);

               // Restore previous async mode
               if(previousAsyncMode) trade.SetAsyncMode(true);
            }
         }
      }
   }

   // Process trailing stops for all positions in a single pass
   TrailActivePositionSL();
}

//+------------------------------------------------------------------+
// Checks and cancels pending orders if market conditions indicate potential reversal
//+------------------------------------------------------------------+
void CheckAndCancelPendingOrders()
{
   if(!CancelOrdersOnExhaustion)
      return; // Feature is disabled

   // Check for upward exhaustion - cancel buy stop orders
   if(IsUpwardExhaustion() && buyStopTicket != 0)
   {
      if(ord.Select((ulong)buyStopTicket))
      {
         // This is important enough to log even without DEBUG
         Print("Upward exhaustion detected. Canceling Buy Stop #", buyStopTicket, " to avoid potential reversal.");
         if(trade.OrderDelete(buyStopTicket))
         {
            // This is important enough to log even without DEBUG
            Print("Successfully canceled Buy Stop #", buyStopTicket);
            buyStopTicket = 0;
            buyStopInfo.ticket = 0;
         }
         else
         {
            // This is important enough to log even without DEBUG
            Print("Failed to cancel Buy Stop #", buyStopTicket, ": ",
                  trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
         }
      }
   }

   // Check for downward exhaustion - cancel sell stop orders
   if(IsDownwardExhaustion() && sellStopTicket != 0)
   {
      if(ord.Select((ulong)sellStopTicket))
      {
         // This is important enough to log even without DEBUG
         Print("Downward exhaustion detected. Canceling Sell Stop #", sellStopTicket, " to avoid potential reversal.");
         if(trade.OrderDelete(sellStopTicket))
         {
            // This is important enough to log even without DEBUG
            Print("Successfully canceled Sell Stop #", sellStopTicket);
            sellStopTicket = 0;
            sellStopInfo.ticket = 0;
         }
         else
         {
            // This is important enough to log even without DEBUG
            Print("Failed to cancel Sell Stop #", sellStopTicket, ": ",
                  trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
         }
      }
   }
}

//+------------------------------------------------------------------+
void ManagePendingOrders(const MqlTick &tick)
{
   // First check if we need to cancel any pending orders due to market exhaustion
   CheckAndCancelPendingOrders();

   // Check for M1 momentum cooldown for Buy orders
   if(TimeCurrent() < g_m1_momentum_buy_cooldown_until)
   {
      if(DEBUG) PrintFormat("Buy momentum filter in cooldown until %s (%d seconds remaining)",
                          TimeToString(g_m1_momentum_buy_cooldown_until),
                          g_m1_momentum_buy_cooldown_until - TimeCurrent());

      // If we have a buy stop order and we're in cooldown, delete it
      if(buyStopTicket != 0)
      {
         string m1_filter_debug_msg = "";
         if(CheckM1StrongCounterCandle(ORDER_TYPE_BUY_STOP, M1MomentumCandlesToCheck, M1MomentumCandleSizeMult, M1AvgRangePeriod, m1_filter_debug_msg))
         {
            if(DEBUG) Print("M1 Momentum Filter: ", m1_filter_debug_msg, " Canceling existing Buy Stop order.");

            if(trade.OrderDelete(buyStopTicket))
            {
               if(DEBUG) Print("Successfully canceled Buy Stop #", buyStopTicket, " due to M1 counter-momentum");
               buyStopTicket = 0;
               buyStopInfo.ticket = 0;
            }
         }
      }
   }
   else
   {
      // Manage Buy Stop order
      if(buyStopTicket != 0)
      {
         // Try to select the order by ticket
         if(!ord.Select((ulong)buyStopTicket))
         {
            Print("ManagePendingOrders: Could not select Buy Stop #", buyStopTicket, " - order may have been filled or canceled");
            buyStopTicket = 0; // Reset the ticket since we can't find the order
         }
         else if(!IsOrderMarketPlaced())
         {
            // Order exists but is not in a valid state
            Print("ManagePendingOrders: Buy Stop #", buyStopTicket, " exists but has invalid state: ",
                  EnumToString(ord.State()));
            buyStopTicket = 0;
         }
         else
         {
            // Check if order is too old (prevent stale pending orders from executing under changed market conditions)
            datetime current_time = TimeCurrent();
            datetime order_time = (datetime)ord.TimeSetup();

            if(current_time - order_time > PendingOrderMaxAgeSeconds)
            {
               if(DEBUG) PrintFormat("Buy Stop #%d is too old (%d seconds). Canceling to prevent execution under changed conditions.",
                                   buyStopTicket, current_time - order_time);

               if(trade.OrderDelete(buyStopTicket))
               {
                  // This is important enough to log even without DEBUG
                  Print("Successfully canceled Buy Stop #", buyStopTicket, " due to age (",
                        current_time - order_time, " seconds)");
                  buyStopTicket = 0;
                  buyStopInfo.ticket = 0;
               }
            }
            // Check for strong counter-momentum that might warrant canceling the order
            else
            {
               string m1_filter_debug_msg = "";
               if(EnableM1MomentumFilter &&
                  CheckM1StrongCounterCandle(ORDER_TYPE_BUY_STOP, M1MomentumCandlesToCheck, M1MomentumCandleSizeMult, M1AvgRangePeriod, m1_filter_debug_msg))
               {
                  if(DEBUG) Print("M1 Momentum Filter: ", m1_filter_debug_msg, " Canceling existing Buy Stop order.");

                  if(trade.OrderDelete(buyStopTicket))
                  {
                     if(DEBUG) Print("Successfully canceled Buy Stop #", buyStopTicket, " due to M1 counter-momentum");
                     buyStopTicket = 0;
                     buyStopInfo.ticket = 0;

                     // Set cooldown period
                     g_m1_momentum_buy_cooldown_until = TimeCurrent() + M1MomentumFilterCooldownBars * 60;
                     if(DEBUG) PrintFormat("M1 Momentum Filter: Cooldown activated for BUY until %s",
                                         TimeToString(g_m1_momentum_buy_cooldown_until));
                  }
               }
               else
               {
                  // Order exists and is valid - trail it
                  TrailPendingBuyStop(tick);
               }
            }
         }
      }

      // Only place a new Buy Stop if we don't have one and we're not in cooldown
      if(buyStopTicket == 0)
      {
         // Check for strong counter-momentum before placing a new order
         string m1_filter_debug_msg = "";
         if(EnableM1MomentumFilter &&
            CheckM1StrongCounterCandle(ORDER_TYPE_BUY_STOP, M1MomentumCandlesToCheck, M1MomentumCandleSizeMult, M1AvgRangePeriod, m1_filter_debug_msg))
         {
            if(DEBUG) Print("M1 Momentum Filter: ", m1_filter_debug_msg, " Cooldown activated for BUY.");

            // Set cooldown period
            g_m1_momentum_buy_cooldown_until = TimeCurrent() + M1MomentumFilterCooldownBars * 60;
            if(DEBUG) PrintFormat("M1 Momentum Filter: Cooldown activated for BUY until %s",
                                TimeToString(g_m1_momentum_buy_cooldown_until));
         }
         else
         {
            PlaceBuyStopOrder(tick);
         }
      }
   }

   // Check for M1 momentum cooldown for Sell orders
   if(TimeCurrent() < g_m1_momentum_sell_cooldown_until)
   {
      if(DEBUG) PrintFormat("Sell momentum filter in cooldown until %s (%d seconds remaining)",
                          TimeToString(g_m1_momentum_sell_cooldown_until),
                          g_m1_momentum_sell_cooldown_until - TimeCurrent());

      // If we have a sell stop order and we're in cooldown, delete it
      if(sellStopTicket != 0)
      {
         string m1_filter_debug_msg = "";
         if(CheckM1StrongCounterCandle(ORDER_TYPE_SELL_STOP, M1MomentumCandlesToCheck, M1MomentumCandleSizeMult, M1AvgRangePeriod, m1_filter_debug_msg))
         {
            if(DEBUG) Print("M1 Momentum Filter: ", m1_filter_debug_msg, " Canceling existing Sell Stop order.");

            if(trade.OrderDelete(sellStopTicket))
            {
               if(DEBUG) Print("Successfully canceled Sell Stop #", sellStopTicket, " due to M1 counter-momentum");
               sellStopTicket = 0;
               sellStopInfo.ticket = 0;
            }
         }
      }
   }
   else
   {
      // Manage Sell Stop order
      if(sellStopTicket != 0)
      {
         if(!ord.Select((ulong)sellStopTicket))
         {
            Print("ManagePendingOrders: Could not select Sell Stop #", sellStopTicket, " - order may have been filled or canceled");
            sellStopTicket = 0;
         }
         else if(!IsOrderMarketPlaced())
         {
            Print("ManagePendingOrders: Sell Stop #", sellStopTicket, " exists but has invalid state: ",
                  EnumToString(ord.State()));
            sellStopTicket = 0;
         }
         else
         {
            // Check if order is too old (prevent stale pending orders from executing under changed market conditions)
            datetime current_time = TimeCurrent();
            datetime order_time = (datetime)ord.TimeSetup();

            if(current_time - order_time > PendingOrderMaxAgeSeconds)
            {
               if(DEBUG) PrintFormat("Sell Stop #%d is too old (%d seconds). Canceling to prevent execution under changed conditions.",
                                   sellStopTicket, current_time - order_time);

               if(trade.OrderDelete(sellStopTicket))
               {
                  // This is important enough to log even without DEBUG
                  Print("Successfully canceled Sell Stop #", sellStopTicket, " due to age (",
                        current_time - order_time, " seconds)");
                  sellStopTicket = 0;
                  sellStopInfo.ticket = 0;
               }
            }
            // Check for strong counter-momentum that might warrant canceling the order
            else
            {
               string m1_filter_debug_msg = "";
               if(EnableM1MomentumFilter &&
                  CheckM1StrongCounterCandle(ORDER_TYPE_SELL_STOP, M1MomentumCandlesToCheck, M1MomentumCandleSizeMult, M1AvgRangePeriod, m1_filter_debug_msg))
               {
                  if(DEBUG) Print("M1 Momentum Filter: ", m1_filter_debug_msg, " Canceling existing Sell Stop order.");

                  if(trade.OrderDelete(sellStopTicket))
                  {
                     if(DEBUG) Print("Successfully canceled Sell Stop #", sellStopTicket, " due to M1 counter-momentum");
                     sellStopTicket = 0;
                     sellStopInfo.ticket = 0;

                     // Set cooldown period
                     g_m1_momentum_sell_cooldown_until = TimeCurrent() + M1MomentumFilterCooldownBars * 60;
                     if(DEBUG) PrintFormat("M1 Momentum Filter: Cooldown activated for SELL until %s",
                                         TimeToString(g_m1_momentum_sell_cooldown_until));
                  }
               }
               else
               {
                  TrailPendingSellStop(tick);
               }
            }
         }
      }

      // Only place a new Sell Stop if we don't have one and we're not in cooldown
      if(sellStopTicket == 0)
      {
         // Check for strong counter-momentum before placing a new order
         string m1_filter_debug_msg = "";
         if(EnableM1MomentumFilter &&
            CheckM1StrongCounterCandle(ORDER_TYPE_SELL_STOP, M1MomentumCandlesToCheck, M1MomentumCandleSizeMult, M1AvgRangePeriod, m1_filter_debug_msg))
         {
            if(DEBUG) Print("M1 Momentum Filter: ", m1_filter_debug_msg, " Cooldown activated for SELL.");

            // Set cooldown period
            g_m1_momentum_sell_cooldown_until = TimeCurrent() + M1MomentumFilterCooldownBars * 60;
            if(DEBUG) PrintFormat("M1 Momentum Filter: Cooldown activated for SELL until %s",
                                TimeToString(g_m1_momentum_sell_cooldown_until));
         }
         else
         {
            PlaceSellStopOrder(tick);
         }
      }
   }
}

//+------------------------------------------------------------------+
bool IsOrderMarketPlaced()
{
    ENUM_ORDER_STATE state = ord.State();

    // Basic verification - just check the main valid states
    if(state == ORDER_STATE_PLACED || state == ORDER_STATE_STARTED || state == ORDER_STATE_PARTIAL)
        return true;

    // Enhanced verification - log unexpected states for debugging
    if(VerificationMode == VERIFICATION_ENHANCED && DEBUG)
    {
        Print("IsOrderMarketPlaced: Order #", ord.Ticket(), " has unexpected state: ",
              EnumToString(state));

        // Some states might be transitional but still valid
        if(state == ORDER_STATE_REQUEST_ADD || state == ORDER_STATE_REQUEST_MODIFY)
        {
            Print("IsOrderMarketPlaced: Order #", ord.Ticket(), " is in transitional state ",
                 EnumToString(state), " - considering valid");
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
void PlaceBuyStopOrder(const MqlTick &tick)
{
   // Check if market is showing signs of upward exhaustion
   if(IsUpwardExhaustion())
   {
      if(DEBUG) Print("Upward exhaustion detected. Skipping Buy Stop placement to avoid potential reversal.");
      return;
   }

   // Check if market conditions are favorable for buy order
   if(!IsBuyConditionMet())
   {
      if(DEBUG) Print("Buy conditions not met based on trend filters. Skipping Buy Stop placement.");
      return;
   }

   // Check for M1 counter-momentum (strong bearish momentum)
   if(EnableM1MomentumFilter && CheckM1CounterMomentum(ORDER_TYPE_BUY_STOP, tick))
   {
      if(DEBUG) Print("M1 counter-momentum (bearish) detected. Skipping Buy Stop placement.");
      return;
   }

   // Check for high volatility
   if(EnableVolatilityFilter && g_isMarketTooVolatile)
   {
      if(DEBUG) Print("High volatility detected. Skipping Buy Stop placement.");
      return;
   }

   // Double-check that we don't already have a buy stop order
   // This is an extra safety check to prevent duplicate orders
   if(buyStopTicket != 0)
   {
      if(DEBUG) Print("PlaceBuyStopOrder: Warning - Attempted to place Buy Stop when ticket #", buyStopTicket, " already exists");
      return;
   }

   // Check if we're in a state that should prevent order creation
   if(StateManagement == STATE_MACHINE &&
      (currentState == STATE_WAITING_FOR_BUY_CONFIRMATION ||
       currentState == STATE_WAITING_FOR_SELL_CONFIRMATION ||
       currentState == STATE_PROCESSING_TRANSACTION))
   {
      if(DEBUG) Print("PlaceBuyStopOrder: Skipping order creation - EA is in ", EnumToString(currentState), " state");
      return;
   }

   // Check if we're in the cooldown period
   if(TimeCurrent() - lastBuyOrderTime < CooldownSeconds)
   {
      if(DEBUG) Print("PlaceBuyStopOrder: Skipping order creation - in cooldown period (",
            CooldownSeconds - (TimeCurrent() - lastBuyOrderTime), " seconds remaining)");
      return;
   }

   // Check if we're processing a transaction
   if(isProcessingTransaction)
   {
      if(DEBUG) Print("PlaceBuyStopOrder: Skipping order creation - transaction in progress");
      return;
   }

   // CRITICAL: Get fresh tick data immediately before order placement
   // This ensures the order price is based on the most current market prices
   MqlTick execution_tick;
   if(!SymbolInfoTick(_Symbol, execution_tick))
   {
      Print("PlaceBuyStopOrder: Failed to get fresh tick data for order placement. Using provided tick.");
      execution_tick = tick; // Fallback to provided tick
   }

   // Calculate the order price based on the freshest possible tick
   double price = NormalizeDouble(execution_tick.ask + OrderDistancePoints * point, symbolDigits);
   double minPrice = NormalizeDouble(execution_tick.ask + (stopLevelPoints + 1) * point, symbolDigits); // +1 for buffer
   if (price < minPrice) price = minPrice;

   // Place the order - this is important enough to log even without DEBUG
   if(DEBUG) Print("Attempting to place Buy Stop at ", DoubleToString(price, symbolDigits),
         " (Ask: ", DoubleToString(execution_tick.ask, symbolDigits),
         "). Trend filters passed: MA=", IsMATrendUp() ? "UP" : "DOWN");

   // Set maximum slippage for this critical operation
   trade.SetDeviationInPoints(MaxSlippagePoints);

   if(trade.BuyStop(Lots, price, _Symbol, 0.0, 0.0, ORDER_TIME_GTC, 0, OrderComment))
   {
      buyStopTicket = (long)trade.ResultOrder();
      lastBuyOrderTime = TimeCurrent();

      // This is important enough to log even without DEBUG
      Print("Successfully placed Buy Stop #", buyStopTicket, " at ", DoubleToString(price, symbolDigits));

      // Update order tracking info
      buyStopInfo.ticket = buyStopTicket;
      buyStopInfo.creationTime = TimeCurrent();
      buyStopInfo.price = price;

      // Verify the order was actually placed with optimized verification process
      bool orderConfirmed = false;

      if(VerificationMode == VERIFICATION_ENHANCED && buyStopTicket != 0)
      {
         // For ASYNC mode, we rely more on the state machine and reduce Sleep time
         // This improves client-side responsiveness while maintaining verification
         if(ExecutionMode == EXECUTION_MODE_ASYNC)
         {
            // Try immediate verification first
            if(ord.Select((ulong)buyStopTicket) && IsOrderMarketPlaced())
            {
               orderConfirmed = true;
               buyStopInfo.lastKnownState = ord.State();
               if(DEBUG) Print("Buy Stop #", buyStopTicket, " confirmed immediately with state: ",
                              EnumToString(ord.State()));
            }
            else
            {
               // If not immediately confirmed, let the state machine handle it
               // This avoids blocking the client with Sleep calls
               if(DEBUG) Print("Buy Stop #", buyStopTicket, " not immediately confirmed. State machine will handle it.");
            }
         }
         // For SYNC mode, we use the traditional retry approach
         else
         {
            int retries = 0;
            while(retries < MaxRetries && !orderConfirmed)
            {
               if(ord.Select((ulong)buyStopTicket) && IsOrderMarketPlaced())
               {
                  orderConfirmed = true;
                  buyStopInfo.lastKnownState = ord.State();
                  if(DEBUG) Print("Buy Stop #", buyStopTicket, " confirmed after ", retries, " retries with state: ",
                                 EnumToString(ord.State()));
               }
               else
               {
                  retries++;
                  if(DEBUG) Print("Retry ", retries, "/", MaxRetries, ": Waiting for Buy Stop #", buyStopTicket, " confirmation...");
                  Sleep(RetryDelayMs); // Small delay between retries
               }
            }

            if(!orderConfirmed)
            {
               // This is important enough to log even without DEBUG
               Print("Warning: Buy Stop #", buyStopTicket, " could not be confirmed after ", MaxRetries, " retries");
            }
         }
      }
      else if(buyStopTicket != 0 && ord.Select((ulong)buyStopTicket))
      {
         // Basic verification
         orderConfirmed = IsOrderMarketPlaced();
         buyStopInfo.lastKnownState = ord.State();
         if(DEBUG) Print("Verified Buy Stop #", buyStopTicket, " exists with state: ",
                        EnumToString(ord.State()));
      }
      else if(DEBUG)
      {
         Print("Warning: Buy Stop order reported success but verification failed");
      }

      // Update state if using state machine
      if(StateManagement == STATE_MACHINE && buyStopTicket != 0)
      {
         if(!orderConfirmed)
         {
            currentState = STATE_WAITING_FOR_BUY_CONFIRMATION;
            stateChangeTime = TimeCurrent();
            if(DEBUG) Print("Changing state to STATE_WAITING_FOR_BUY_CONFIRMATION for ticket #", buyStopTicket);
         }
      }
   }
   else
   {
      // This is important enough to log even without DEBUG
      Print("Error placing Buy Stop: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription(),
            ". Target: ", DoubleToString(price, symbolDigits),
            ", Ask: ", DoubleToString(tick.ask, symbolDigits),
            ", MinPrice: ", DoubleToString(minPrice, symbolDigits),
            " (StopLevel: ", stopLevelPoints, ")");
   }
}

//+------------------------------------------------------------------+
void PlaceSellStopOrder(const MqlTick &tick)
{
   // Check if market is showing signs of downward exhaustion
   if(IsDownwardExhaustion())
   {
      if(DEBUG) Print("Downward exhaustion detected. Skipping Sell Stop placement to avoid potential reversal.");
      return;
   }

   // Check if market conditions are favorable for sell order
   if(!IsSellConditionMet())
   {
      if(DEBUG) Print("Sell conditions not met based on trend filters. Skipping Sell Stop placement.");
      return;
   }

   // Check for M1 counter-momentum (strong bullish momentum)
   if(EnableM1MomentumFilter && CheckM1CounterMomentum(ORDER_TYPE_SELL_STOP, tick))
   {
      if(DEBUG) Print("M1 counter-momentum (bullish) detected. Skipping Sell Stop placement.");
      return;
   }

   // Check for high volatility
   if(EnableVolatilityFilter && g_isMarketTooVolatile)
   {
      if(DEBUG) Print("High volatility detected. Skipping Sell Stop placement.");
      return;
   }

   // Double-check that we don't already have a sell stop order
   // This is an extra safety check to prevent duplicate orders
   if(sellStopTicket != 0)
   {
      if(DEBUG) Print("PlaceSellStopOrder: Warning - Attempted to place Sell Stop when ticket #", sellStopTicket, " already exists");
      return;
   }

   // Check if we're in a state that should prevent order creation
   if(StateManagement == STATE_MACHINE &&
      (currentState == STATE_WAITING_FOR_BUY_CONFIRMATION ||
       currentState == STATE_WAITING_FOR_SELL_CONFIRMATION ||
       currentState == STATE_PROCESSING_TRANSACTION))
   {
      if(DEBUG) Print("PlaceSellStopOrder: Skipping order creation - EA is in ", EnumToString(currentState), " state");
      return;
   }

   // Check if we're in the cooldown period
   if(TimeCurrent() - lastSellOrderTime < CooldownSeconds)
   {
      if(DEBUG) Print("PlaceSellStopOrder: Skipping order creation - in cooldown period (",
            CooldownSeconds - (TimeCurrent() - lastSellOrderTime), " seconds remaining)");
      return;
   }

   // Check if we're processing a transaction
   if(isProcessingTransaction)
   {
      if(DEBUG) Print("PlaceSellStopOrder: Skipping order creation - transaction in progress");
      return;
   }

   // CRITICAL: Get fresh tick data immediately before order placement
   // This ensures the order price is based on the most current market prices
   MqlTick execution_tick;
   if(!SymbolInfoTick(_Symbol, execution_tick))
   {
      Print("PlaceSellStopOrder: Failed to get fresh tick data for order placement. Using provided tick.");
      execution_tick = tick; // Fallback to provided tick
   }

   // Calculate the order price based on the freshest possible tick
   double price = NormalizeDouble(execution_tick.bid - OrderDistancePoints * point, symbolDigits);
   double maxPrice = NormalizeDouble(execution_tick.bid - (stopLevelPoints + 1) * point, symbolDigits); // +1 for buffer
   if (price > maxPrice) price = maxPrice;

   // Place the order
   if(DEBUG) Print("Attempting to place Sell Stop at ", DoubleToString(price, symbolDigits),
         " (Bid: ", DoubleToString(execution_tick.bid, symbolDigits),
         "). Trend filters passed: MA=", IsMATrendUp() ? "UP" : "DOWN");

   // Set maximum slippage for this critical operation
   trade.SetDeviationInPoints(MaxSlippagePoints);

   if(trade.SellStop(Lots, price, _Symbol, 0.0, 0.0, ORDER_TIME_GTC, 0, OrderComment))
   {
      sellStopTicket = (long)trade.ResultOrder();
      lastSellOrderTime = TimeCurrent();

      // This is important enough to log even without DEBUG
      Print("Successfully placed Sell Stop #", sellStopTicket, " at ", DoubleToString(price, symbolDigits));

      // Update order tracking info
      sellStopInfo.ticket = sellStopTicket;
      sellStopInfo.creationTime = TimeCurrent();
      sellStopInfo.price = price;

      // Verify the order was actually placed with optimized verification process
      bool orderConfirmed = false;

      if(VerificationMode == VERIFICATION_ENHANCED && sellStopTicket != 0)
      {
         // For ASYNC mode, we rely more on the state machine and reduce Sleep time
         // This improves client-side responsiveness while maintaining verification
         if(ExecutionMode == EXECUTION_MODE_ASYNC)
         {
            // Try immediate verification first
            if(ord.Select((ulong)sellStopTicket) && IsOrderMarketPlaced())
            {
               orderConfirmed = true;
               sellStopInfo.lastKnownState = ord.State();
               if(DEBUG) Print("Sell Stop #", sellStopTicket, " confirmed immediately with state: ",
                              EnumToString(ord.State()));
            }
            else
            {
               // If not immediately confirmed, let the state machine handle it
               // This avoids blocking the client with Sleep calls
               if(DEBUG) Print("Sell Stop #", sellStopTicket, " not immediately confirmed. State machine will handle it.");
            }
         }
         // For SYNC mode, we use the traditional retry approach
         else
         {
            int retries = 0;
            while(retries < MaxRetries && !orderConfirmed)
            {
               if(ord.Select((ulong)sellStopTicket) && IsOrderMarketPlaced())
               {
                  orderConfirmed = true;
                  sellStopInfo.lastKnownState = ord.State();
                  if(DEBUG) Print("Sell Stop #", sellStopTicket, " confirmed after ", retries, " retries with state: ",
                                 EnumToString(ord.State()));
               }
               else
               {
                  retries++;
                  if(DEBUG) Print("Retry ", retries, "/", MaxRetries, ": Waiting for Sell Stop #", sellStopTicket, " confirmation...");
                  Sleep(RetryDelayMs); // Small delay between retries
               }
            }

            if(!orderConfirmed)
            {
               // This is important enough to log even without DEBUG
               Print("Warning: Sell Stop #", sellStopTicket, " could not be confirmed after ", MaxRetries, " retries");
            }
         }
      }
      else if(sellStopTicket != 0 && ord.Select((ulong)sellStopTicket))
      {
         // Basic verification
         orderConfirmed = IsOrderMarketPlaced();
         sellStopInfo.lastKnownState = ord.State();
         if(DEBUG) Print("Verified Sell Stop #", sellStopTicket, " exists with state: ",
                        EnumToString(ord.State()));
      }
      else if(DEBUG)
      {
         Print("Warning: Sell Stop order reported success but verification failed");
      }

      // Update state if using state machine
      if(StateManagement == STATE_MACHINE && sellStopTicket != 0)
      {
         if(!orderConfirmed)
         {
            currentState = STATE_WAITING_FOR_SELL_CONFIRMATION;
            stateChangeTime = TimeCurrent();
            if(DEBUG) Print("Changing state to STATE_WAITING_FOR_SELL_CONFIRMATION for ticket #", sellStopTicket);
         }
      }
   }
   else
   {
      // This is important enough to log even without DEBUG
      Print("Error placing Sell Stop: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription(),
            ". Target: ", DoubleToString(price, symbolDigits),
            ", Bid: ", DoubleToString(tick.bid, symbolDigits),
            ", MaxPrice: ", DoubleToString(maxPrice, symbolDigits),
            " (StopLevel: ", stopLevelPoints, ")");
   }
}

//+------------------------------------------------------------------+
void TrailPendingBuyStop(const MqlTick &tick)
{
   if(buyStopTicket == 0)
      return;

   if(!ord.Select((ulong)buyStopTicket))
   {
      if(DEBUG) Print("TrailPendingBuyStop: Could not select Buy Stop #", buyStopTicket);
      return;
   }

   // CRITICAL: Get fresh tick data immediately before order modification
   // This ensures the order price is based on the most current market prices
   MqlTick execution_tick;
   if(!SymbolInfoTick(_Symbol, execution_tick))
   {
      if(DEBUG) Print("TrailPendingBuyStop: Failed to get fresh tick data for order modification. Using provided tick.");
      execution_tick = tick; // Fallback to provided tick
   }

   double currentOrderPrice = ord.PriceOpen();
   double desiredPrice = NormalizeDouble(execution_tick.ask + OrderDistancePoints * point, symbolDigits);
   double minAllowedPrice = NormalizeDouble(execution_tick.ask + (stopLevelPoints + 1) * point, symbolDigits);
   if (desiredPrice < minAllowedPrice) desiredPrice = minAllowedPrice;

   // Only move "down" towards current price if the change is significant enough
   if(desiredPrice < currentOrderPrice && MathAbs(desiredPrice - currentOrderPrice) >= MinimumSLUpdatePoints * point)
   {
      // Set maximum slippage for this operation
      trade.SetDeviationInPoints(MaxSlippagePoints);

      if(trade.OrderModify(buyStopTicket, desiredPrice, 0.0, 0.0, ORDER_TIME_GTC, 0))
      {
         if(DEBUG) Print("Modified Buy Stop #", buyStopTicket, " from ",
                        DoubleToString(currentOrderPrice, symbolDigits), " to ",
                        DoubleToString(desiredPrice, symbolDigits));
      }
      else
      {
         // This is important enough to log even without DEBUG
         Print("Error modifying Buy Stop #", buyStopTicket, " (Trail): ",
               trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription(),
               ". CurP: ", DoubleToString(currentOrderPrice, symbolDigits),
               ", NewP: ", DoubleToString(desiredPrice, symbolDigits));
      }
   }
}

//+------------------------------------------------------------------+
void TrailPendingSellStop(const MqlTick &tick)
{
   if(sellStopTicket == 0)
      return;

   if(!ord.Select((ulong)sellStopTicket))
   {
      if(DEBUG) Print("TrailPendingSellStop: Could not select Sell Stop #", sellStopTicket);
      return;
   }

   // CRITICAL: Get fresh tick data immediately before order modification
   // This ensures the order price is based on the most current market prices
   MqlTick execution_tick;
   if(!SymbolInfoTick(_Symbol, execution_tick))
   {
      if(DEBUG) Print("TrailPendingSellStop: Failed to get fresh tick data for order modification. Using provided tick.");
      execution_tick = tick; // Fallback to provided tick
   }

   double currentOrderPrice = ord.PriceOpen();
   double desiredPrice = NormalizeDouble(execution_tick.bid - OrderDistancePoints * point, symbolDigits);
   double maxAllowedPrice = NormalizeDouble(execution_tick.bid - (stopLevelPoints + 1) * point, symbolDigits);
   if (desiredPrice > maxAllowedPrice) desiredPrice = maxAllowedPrice;

   // Only move "up" towards current price if the change is significant enough
   if(desiredPrice > currentOrderPrice && MathAbs(desiredPrice - currentOrderPrice) >= MinimumSLUpdatePoints * point)
   {
      // Set maximum slippage for this operation
      trade.SetDeviationInPoints(MaxSlippagePoints);

      if(trade.OrderModify(sellStopTicket, desiredPrice, 0.0, 0.0, ORDER_TIME_GTC, 0))
      {
         if(DEBUG) Print("Modified Sell Stop #", sellStopTicket, " from ",
                        DoubleToString(currentOrderPrice, symbolDigits), " to ",
                        DoubleToString(desiredPrice, symbolDigits));
      }
      else
      {
         // This is important enough to log even without DEBUG
         Print("Error modifying Sell Stop #", sellStopTicket, " (Trail): ",
               trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription(),
               ". CurP: ", DoubleToString(currentOrderPrice, symbolDigits),
               ", NewP: ", DoubleToString(desiredPrice, symbolDigits));
      }
   }
}

//+------------------------------------------------------------------+
// Sets initial stop loss for a position
//+------------------------------------------------------------------+
void SetInitialStopLoss(ulong position_ticket, ENUM_POSITION_TYPE position_type, const MqlTick &tick_for_validation)
{
   // CRITICAL: Get fresh tick data for accurate SL calculation
   // This ensures SL is set based on current market prices, not potentially stale tick data
   MqlTick fresh_tick_for_sl_calc;
   if(!SymbolInfoTick(_Symbol, fresh_tick_for_sl_calc))
   {
      Print("SetInitialSL: Failed to get fresh tick data for SL calculation. Using provided tick.");
      fresh_tick_for_sl_calc = tick_for_validation; // Fallback to provided tick
   }

   if(!pos.SelectByTicket(position_ticket))
   {
      Print("SetInitialSL: Could not select position #", position_ticket);
      return;
   }

   double openPrice = pos.PriceOpen();
   double slPrice = 0;
   double sl_offset_value = 0;
   double stopLevelDistance = (stopLevelPoints + 3) * point; // Min distance from current price (+3 for safety)

   // Get current market prices from fresh tick
   double ask = fresh_tick_for_sl_calc.ask;
   double bid = fresh_tick_for_sl_calc.bid;

   // Calculate SL offset based on selected mode
   switch(InitialSLMode)
   {
      case SL_MODE_POINTS:
         sl_offset_value = InitialSLPoints * point;
         break;

      case SL_MODE_PERCENTAGE:
         sl_offset_value = openPrice * (InitialSLPercentage / 100.0);
         if(sl_offset_value < point) sl_offset_value = point; // Ensure minimum 1 point
         break;

      case SL_MODE_ATR:
         {
            double atrVal = GetAtrValue();
            if(atrHandle == INVALID_HANDLE || atrVal <= (point * 0.1))
               sl_offset_value = InitialSLPoints * point; // Fallback to points
            else
               sl_offset_value = atrVal * AtrMultiplier;
         }
         break;

      default:
         sl_offset_value = InitialSLPoints * point; // Default to points
         break;
   }

   // Calculate initial SL price based on position type
   if(position_type == POSITION_TYPE_BUY)
   {
      // For buy positions, SL is below open price
      slPrice = NormalizeDouble(openPrice - sl_offset_value, symbolDigits);

      // Ensure SL is below open price
      if(slPrice >= openPrice)
         slPrice = NormalizeDouble(openPrice - stopLevelDistance, symbolDigits);

      // Ensure SL respects broker's stop level from current market
      double maxAllowedSL = NormalizeDouble(bid - stopLevelDistance, symbolDigits);
      if(slPrice > maxAllowedSL)
         slPrice = maxAllowedSL;
   }
   else // POSITION_TYPE_SELL
   {
      // For sell positions, SL is above open price
      slPrice = NormalizeDouble(openPrice + sl_offset_value, symbolDigits);

      // Ensure SL is above open price
      if(slPrice <= openPrice)
         slPrice = NormalizeDouble(openPrice + stopLevelDistance, symbolDigits);

      // Ensure SL respects broker's stop level from current market
      double minAllowedSL = NormalizeDouble(ask + stopLevelDistance, symbolDigits);
      if(slPrice < minAllowedSL)
         slPrice = minAllowedSL;
   }

   slPrice = NormalizeDouble(slPrice, symbolDigits);

   // Final validation
   bool validSL = (position_type == POSITION_TYPE_BUY && slPrice < openPrice && slPrice != 0.0) ||
                  (position_type == POSITION_TYPE_SELL && slPrice > openPrice && slPrice != 0.0);

   if(validSL)
   {
      // Use synchronous mode for this critical operation
      bool previousAsyncMode = (ExecutionMode == EXECUTION_MODE_ASYNC);
      if(previousAsyncMode) trade.SetAsyncMode(false);

      // Set maximum slippage for this critical operation
      trade.SetDeviationInPoints(MaxSlippagePoints);

      // Set the stop loss
      if(trade.PositionModify(position_ticket, slPrice, pos.TakeProfit()))
      {
         // This is important enough to log even without DEBUG
         Print("Set Initial SL for Pos #", position_ticket, " (", EnumToString(position_type),
               ") to ", DoubleToString(slPrice, symbolDigits), ". Open: ",
               DoubleToString(openPrice, symbolDigits));
      }
      else
      {
         // One retry with fresh market data
         MqlTick fresh_tick;
         if(SymbolInfoTick(_Symbol, fresh_tick))
         {
            // Recalculate SL based on new market data
            if(position_type == POSITION_TYPE_BUY)
            {
               double maxAllowedSL = NormalizeDouble(fresh_tick.bid - stopLevelDistance, symbolDigits);
               if(slPrice > maxAllowedSL)
                  slPrice = maxAllowedSL;
            }
            else // SELL
            {
               double minAllowedSL = NormalizeDouble(fresh_tick.ask + stopLevelDistance, symbolDigits);
               if(slPrice < minAllowedSL)
                  slPrice = minAllowedSL;
            }

            // Try again with adjusted SL
            // Set maximum slippage for this critical operation
            trade.SetDeviationInPoints(MaxSlippagePoints);

            if(trade.PositionModify(position_ticket, slPrice, pos.TakeProfit()))
            {
               // This is important enough to log even without DEBUG
               Print("Set Initial SL for Pos #", position_ticket, " (", EnumToString(position_type),
                     ") to ", DoubleToString(slPrice, symbolDigits), " on retry");
            }
            else
            {
               // This is important enough to log even without DEBUG
               Print("Failed to set Initial SL for Pos #", position_ticket, ": ",
                     trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
            }
         }
      }

      // Restore previous async mode
      if(previousAsyncMode) trade.SetAsyncMode(true);
   }
   else
   {
      // This is important enough to log even without DEBUG
      Print("Invalid SL calculated for Pos #", position_ticket, ": ",
            DoubleToString(slPrice, symbolDigits), ", Open: ", DoubleToString(openPrice, symbolDigits));
   }
}

//+------------------------------------------------------------------+
double GetAtrValue()
{
   if(atrHandle == INVALID_HANDLE)
   {
       if(DEBUG) Print("GetAtrValue: ATR handle is invalid. Cannot get ATR.");
       return 0.0;
   }

   // Use static array to avoid repeated allocations
   static double atr_buffer[1];

   // Copy the ATR value of the last completed bar (index 1)
   // This is intentional for stability - using completed bars provides more reliable ATR values
   // than the current forming bar which can be volatile
   if(CopyBuffer(atrHandle, 0, 1, 1, atr_buffer) == 1)
   {
      return NormalizeDouble(atr_buffer[0], symbolDigits > 0 ? symbolDigits : 5); // Ensure precision for ATR
   }

   if(DEBUG) Print("Error copying ATR buffer for ", _Symbol, ": ", GetLastError(), ". Handle: ", atrHandle);
   return 0.0;
}

//+------------------------------------------------------------------+
// Checks for bearish price action patterns (potential top)
//+------------------------------------------------------------------+
bool IsBearishPriceActionPattern()
{
   if(!CheckPriceActionPatterns)
      return false;

   // Get price data
   MqlRates rates[];
   ArraySetAsSeries(rates, true);

   if(CopyRates(_Symbol, ExhaustionTimeframe, 0, PriceActionBars + 1, rates) <= 0)
      return false;

   // Check for bearish engulfing pattern
   bool bearishEngulfing = (rates[1].close > rates[1].open) && // Previous candle is bullish
                          (rates[0].open > rates[1].close) &&  // Current candle opens above previous close
                          (rates[0].close < rates[1].open) &&  // Current candle closes below previous open
                          (rates[0].close < rates[0].open);    // Current candle is bearish

   // Check for shooting star pattern
   bool shootingStar = (rates[1].close > rates[1].open) &&                      // Previous candle is bullish
                       (rates[0].high - rates[0].open > 2 * (rates[0].open - rates[0].close)) && // Long upper wick
                       ((rates[0].open - rates[0].close) > 0) &&                // Bearish candle
                       ((rates[0].close - rates[0].low) < (rates[0].open - rates[0].close) * 0.3); // Small or no lower wick

   // Check for evening star pattern (simplified)
   bool eveningStar = (PriceActionBars >= 3) &&
                      (rates[2].close > rates[2].open) && // First candle is bullish
                      (MathAbs(rates[1].close - rates[1].open) < (rates[2].high - rates[2].low) * 0.3) && // Second candle is small
                      (rates[0].close < rates[0].open) && // Third candle is bearish
                      (rates[0].close < (rates[2].open + rates[2].close) / 2); // Third candle closes below midpoint of first

   // Check for double top pattern (simplified)
   bool doubleTop = false;
   if(PriceActionBars >= 5)
   {
      double firstPeak = -1, secondPeak = -1;
      int firstPeakIdx = -1;

      // Find the highest high in the first half of the bars
      for(int i = PriceActionBars; i > PriceActionBars/2; i--)
      {
         if(firstPeak < rates[i].high)
         {
            firstPeak = rates[i].high;
            firstPeakIdx = i;
         }
      }

      // Find the highest high in the second half of the bars
      for(int i = PriceActionBars/2; i >= 0; i--)
      {
         if(secondPeak < rates[i].high)
            secondPeak = rates[i].high;
      }

      // Check if we have two similar peaks with a valley in between
      if(firstPeakIdx != -1 &&
         MathAbs(firstPeak - secondPeak) < (firstPeak * 0.003) && // Peaks within 0.3% of each other
         rates[firstPeakIdx/2].low < firstPeak * 0.997) // Valley at least 0.3% below peaks
      {
         doubleTop = true;
      }
   }

   return (bearishEngulfing || shootingStar || eveningStar || doubleTop);
}

//+------------------------------------------------------------------+
// Checks for bullish price action patterns (potential bottom)
//+------------------------------------------------------------------+
bool IsBullishPriceActionPattern()
{
   if(!CheckPriceActionPatterns)
      return false;

   // Get price data
   MqlRates rates[];
   ArraySetAsSeries(rates, true);

   if(CopyRates(_Symbol, ExhaustionTimeframe, 0, PriceActionBars + 1, rates) <= 0)
      return false;

   // Check for bullish engulfing pattern
   bool bullishEngulfing = (rates[1].close < rates[1].open) && // Previous candle is bearish
                          (rates[0].open < rates[1].close) &&  // Current candle opens below previous close
                          (rates[0].close > rates[1].open) &&  // Current candle closes above previous open
                          (rates[0].close > rates[0].open);    // Current candle is bullish

   // Check for hammer pattern
   bool hammer = (rates[1].close < rates[1].open) &&                      // Previous candle is bearish
                 (rates[0].low - rates[0].close > 2 * (rates[0].close - rates[0].open)) && // Long lower wick
                 ((rates[0].close - rates[0].open) > 0) &&                // Bullish candle
                 ((rates[0].high - rates[0].close) < (rates[0].close - rates[0].open) * 0.3); // Small or no upper wick

   // Check for morning star pattern (simplified)
   bool morningStar = (PriceActionBars >= 3) &&
                      (rates[2].close < rates[2].open) && // First candle is bearish
                      (MathAbs(rates[1].close - rates[1].open) < (rates[2].high - rates[2].low) * 0.3) && // Second candle is small
                      (rates[0].close > rates[0].open) && // Third candle is bullish
                      (rates[0].close > (rates[2].open + rates[2].close) / 2); // Third candle closes above midpoint of first

   // Check for double bottom pattern (simplified)
   bool doubleBottom = false;
   if(PriceActionBars >= 5)
   {
      double firstBottom = DBL_MAX, secondBottom = DBL_MAX;
      int firstBottomIdx = -1;

      // Find the lowest low in the first half of the bars
      for(int i = PriceActionBars; i > PriceActionBars/2; i--)
      {
         if(firstBottom > rates[i].low)
         {
            firstBottom = rates[i].low;
            firstBottomIdx = i;
         }
      }

      // Find the lowest low in the second half of the bars
      for(int i = PriceActionBars/2; i >= 0; i--)
      {
         if(secondBottom > rates[i].low)
            secondBottom = rates[i].low;
      }

      // Check if we have two similar bottoms with a peak in between
      if(firstBottomIdx != -1 &&
         MathAbs(firstBottom - secondBottom) < (firstBottom * 0.003) && // Bottoms within 0.3% of each other
         rates[firstBottomIdx/2].high > firstBottom * 1.003) // Peak at least 0.3% above bottoms
      {
         doubleBottom = true;
      }
   }

   return (bullishEngulfing || hammer || morningStar || doubleBottom);
}

//+------------------------------------------------------------------+
// Checks if market is showing signs of upward exhaustion (overbought)
//+------------------------------------------------------------------+
bool IsUpwardExhaustion()
{
   // Check for bearish price action patterns
   if(IsBearishPriceActionPattern())
      return true;

   // If exhaustion detection is disabled, always return false (no exhaustion)
   if(ExhaustionMode == EXHAUSTION_OFF)
      return false;

   bool rsiOverbought = false;
   bool stochOverbought = false;

   // Check RSI if enabled
   if(ExhaustionMode == EXHAUSTION_RSI || ExhaustionMode == EXHAUSTION_BOTH)
   {
      if(rsiHandle == INVALID_HANDLE)
         return false; // Default to false if indicator not available

      // Use static arrays to avoid repeated allocations
      static double rsiBuffer[];

      // Only resize if needed
      if(ArraySize(rsiBuffer) < 2)
         ArrayResize(rsiBuffer, 2, 0);

      ArraySetAsSeries(rsiBuffer, true);

      // Get values from shift 1 (last completed bar)
      // This ensures we're using completed bars for more stable exhaustion detection
      int copied = CopyBuffer(rsiHandle, 0, 1, 2, rsiBuffer);

      if(copied <= 0)
      {
         if(DEBUG) Print("IsUpwardExhaustion: Failed to copy RSI data. Error: ", GetLastError());
         return false; // Default to false if data not available
      }

      // Check if RSI is in overbought territory (using last completed bar)
      rsiOverbought = rsiBuffer[0] >= RSI_UpperThreshold;

      // If using only RSI, return its result
      if(ExhaustionMode == EXHAUSTION_RSI)
         return rsiOverbought;
   }

   // Check Stochastic if enabled
   if(ExhaustionMode == EXHAUSTION_STOCH || ExhaustionMode == EXHAUSTION_BOTH)
   {
      if(stochHandle == INVALID_HANDLE)
         return false; // Default to false if indicator not available

      // Use static arrays to avoid repeated allocations
      static double kBuffer[], dBuffer[];

      // Only resize if needed
      if(ArraySize(kBuffer) < 2)
         ArrayResize(kBuffer, 2, 0);
      if(ArraySize(dBuffer) < 2)
         ArrayResize(dBuffer, 2, 0);

      ArraySetAsSeries(kBuffer, true);
      ArraySetAsSeries(dBuffer, true);

      // Get values from shift 1 (last completed bar)
      int copied1 = CopyBuffer(stochHandle, 0, 1, 2, kBuffer); // %K line
      int copied2 = CopyBuffer(stochHandle, 1, 1, 2, dBuffer); // %D line

      if(copied1 <= 0 || copied2 <= 0)
      {
         if(DEBUG) Print("IsUpwardExhaustion: Failed to copy Stochastic data. Error: ", GetLastError());
         return false; // Default to false if data not available
      }

      // Check if both Stochastic lines are in overbought territory (using last completed bar)
      stochOverbought = (kBuffer[0] >= Stoch_UpperThreshold && dBuffer[0] >= Stoch_UpperThreshold);

      // If using only Stochastic, return its result
      if(ExhaustionMode == EXHAUSTION_STOCH)
         return stochOverbought;
   }

   // If either indicator shows exhaustion, check for contradictory M1 momentum
   if(rsiOverbought || stochOverbought)
   {
      // Check if we have strong bullish momentum on M1 that contradicts the exhaustion signal
      string m1_check_debug = "";
      bool strong_m1_bullish_momentum_persists = CheckM1StrongCounterCandle(ORDER_TYPE_SELL_STOP, 1, 1.0, M1AvgRangePeriod, m1_check_debug);

      if(strong_m1_bullish_momentum_persists)
      {
         if(DEBUG) PrintFormat("IsUpwardExhaustion: Stoch/RSI overbought BUT strong M1 bullish momentum persists (%s). Not flagging exhaustion.",
                             m1_check_debug);
         return false; // Don't flag exhaustion when M1 is still aggressively bullish
      }
   }

   // If both indicators are enabled, return true if either shows exhaustion
   return (rsiOverbought || stochOverbought);
}

//+------------------------------------------------------------------+
// Checks if market is showing signs of downward exhaustion (oversold)
//+------------------------------------------------------------------+
bool IsDownwardExhaustion()
{
   // Check for bullish price action patterns
   if(IsBullishPriceActionPattern())
      return true;

   // If exhaustion detection is disabled, always return false (no exhaustion)
   if(ExhaustionMode == EXHAUSTION_OFF)
      return false;

   bool rsiOversold = false;
   bool stochOversold = false;

   // Check RSI if enabled
   if(ExhaustionMode == EXHAUSTION_RSI || ExhaustionMode == EXHAUSTION_BOTH)
   {
      if(rsiHandle == INVALID_HANDLE)
         return false; // Default to false if indicator not available

      // Use static arrays to avoid repeated allocations
      static double rsiBuffer[];

      // Only resize if needed
      if(ArraySize(rsiBuffer) < 2)
         ArrayResize(rsiBuffer, 2, 0);

      ArraySetAsSeries(rsiBuffer, true);

      // Get values from shift 1 (last completed bar)
      // This ensures we're using completed bars for more stable exhaustion detection
      int copied = CopyBuffer(rsiHandle, 0, 1, 2, rsiBuffer);

      if(copied <= 0)
      {
         if(DEBUG) Print("IsDownwardExhaustion: Failed to copy RSI data. Error: ", GetLastError());
         return false; // Default to false if data not available
      }

      // Check if RSI is in oversold territory (using last completed bar)
      rsiOversold = rsiBuffer[0] <= RSI_LowerThreshold;

      // If using only RSI, return its result
      if(ExhaustionMode == EXHAUSTION_RSI)
         return rsiOversold;
   }

   // Check Stochastic if enabled
   if(ExhaustionMode == EXHAUSTION_STOCH || ExhaustionMode == EXHAUSTION_BOTH)
   {
      if(stochHandle == INVALID_HANDLE)
         return false; // Default to false if indicator not available

      // Use static arrays to avoid repeated allocations
      static double kBuffer[], dBuffer[];

      // Only resize if needed
      if(ArraySize(kBuffer) < 2)
         ArrayResize(kBuffer, 2, 0);
      if(ArraySize(dBuffer) < 2)
         ArrayResize(dBuffer, 2, 0);

      ArraySetAsSeries(kBuffer, true);
      ArraySetAsSeries(dBuffer, true);

      // Get values from shift 1 (last completed bar)
      int copied1 = CopyBuffer(stochHandle, 0, 1, 2, kBuffer); // %K line
      int copied2 = CopyBuffer(stochHandle, 1, 1, 2, dBuffer); // %D line

      if(copied1 <= 0 || copied2 <= 0)
      {
         if(DEBUG) Print("IsDownwardExhaustion: Failed to copy Stochastic data. Error: ", GetLastError());
         return false; // Default to false if data not available
      }

      // Check if both Stochastic lines are in oversold territory (using last completed bar)
      stochOversold = (kBuffer[0] <= Stoch_LowerThreshold && dBuffer[0] <= Stoch_LowerThreshold);

      // If using only Stochastic, return its result
      if(ExhaustionMode == EXHAUSTION_STOCH)
         return stochOversold;
   }

   // If either indicator shows exhaustion, check for contradictory M1 momentum
   if(rsiOversold || stochOversold)
   {
      // Check if we have strong bearish momentum on M1 that contradicts the exhaustion signal
      string m1_check_debug = "";
      bool strong_m1_bearish_momentum_persists = CheckM1StrongCounterCandle(ORDER_TYPE_BUY_STOP, 1, 1.0, M1AvgRangePeriod, m1_check_debug);

      if(strong_m1_bearish_momentum_persists)
      {
         if(DEBUG) PrintFormat("IsDownwardExhaustion: Stoch/RSI oversold BUT strong M1 bearish momentum persists (%s). Not flagging exhaustion.",
                             m1_check_debug);
         return false; // Don't flag exhaustion when M1 is still aggressively bearish
      }
   }

   // If both indicators are enabled, return true if either shows exhaustion
   return (rsiOversold || stochOversold);
}

//+------------------------------------------------------------------+
// Returns true if MA trend is up, false if down
//+------------------------------------------------------------------+
bool IsMATrendUp()
{
   if(fastMAHandle == INVALID_HANDLE || slowMAHandle == INVALID_HANDLE)
      return true; // Default to true if indicators not available

   // Use static arrays to avoid repeated allocations
   static double fastMABuffer[], slowMABuffer[];

   // Only resize if needed
   if(ArraySize(fastMABuffer) < 2)
      ArrayResize(fastMABuffer, 2, 0);
   if(ArraySize(slowMABuffer) < 2)
      ArrayResize(slowMABuffer, 2, 0);

   ArraySetAsSeries(fastMABuffer, true);
   ArraySetAsSeries(slowMABuffer, true);

   // Get values from shift 1 onwards (last completed bar and the one before it)
   // This ensures we're using completed bars for more stable trend assessment
   int copied1 = CopyBuffer(fastMAHandle, 0, 1, 2, fastMABuffer);
   int copied2 = CopyBuffer(slowMAHandle, 0, 1, 2, slowMABuffer);

   if(copied1 <= 0 || copied2 <= 0)
   {
      if(DEBUG) Print("IsMATrendUp: Failed to copy MA data. Error: ", GetLastError());
      return true; // Default to true if data not available
   }

   // Current values (last completed bar)
   bool fastAboveSlow = fastMABuffer[0] > slowMABuffer[0];

   // Previous values (bar before the last completed bar)
   bool prevFastAboveSlow = fastMABuffer[1] > slowMABuffer[1];

   // Trend is up if fast MA is above slow MA and was also above in previous bar
   return fastAboveSlow && prevFastAboveSlow;
}

//+------------------------------------------------------------------+
// Returns true if ADX indicates strong trend
//+------------------------------------------------------------------+
bool IsADXTrendStrong(bool &isUp)
{
   if(adxHandle == INVALID_HANDLE)
      return true; // Default to true if indicator not available

   // Use static arrays to avoid repeated allocations
   static double adxBuffer[], plusDIBuffer[], minusDIBuffer[];

   // Only resize if needed
   if(ArraySize(adxBuffer) < 1)
      ArrayResize(adxBuffer, 1, 0);
   if(ArraySize(plusDIBuffer) < 1)
      ArrayResize(plusDIBuffer, 1, 0);
   if(ArraySize(minusDIBuffer) < 1)
      ArrayResize(minusDIBuffer, 1, 0);

   ArraySetAsSeries(adxBuffer, true);
   ArraySetAsSeries(plusDIBuffer, true);
   ArraySetAsSeries(minusDIBuffer, true);

   // Get values from shift 1 (last completed bar)
   // This ensures we're using completed bars for more stable trend assessment
   int copied = CopyBuffer(adxHandle, 0, 1, 1, adxBuffer);       // Main ADX line
   int copied1 = CopyBuffer(adxHandle, 1, 1, 1, plusDIBuffer);   // +DI line
   int copied2 = CopyBuffer(adxHandle, 2, 1, 1, minusDIBuffer);  // -DI line

   if(copied <= 0 || copied1 <= 0 || copied2 <= 0)
   {
      if(DEBUG) Print("IsADXTrendStrong: Failed to copy ADX data. Error: ", GetLastError());
      return true; // Default to true if data not available
   }

   // Check if ADX is above threshold (strong trend)
   bool strongTrend = adxBuffer[0] >= ADX_Threshold;

   // Determine trend direction based on DI+ and DI-
   isUp = plusDIBuffer[0] > minusDIBuffer[0];

   return strongTrend;
}

//+------------------------------------------------------------------+
// Checks if market conditions are favorable for buy order
//+------------------------------------------------------------------+
bool IsBuyConditionMet()
{
   // If trend filtering is disabled, always return true
   if(TrendFilterMode == TREND_FILTER_OFF)
      return true;

   bool adxUp = false;
   bool adxStrong = false;
   bool maTrendUp = false;

   // Check MA trend if enabled
   if(TrendFilterMode == TREND_FILTER_MA || TrendFilterMode == TREND_FILTER_BOTH)
   {
      maTrendUp = IsMATrendUp();
      if(TrendFilterMode == TREND_FILTER_MA && !maTrendUp)
         return false; // MA trend is down, don't buy
   }

   // Check ADX trend if enabled
   if(TrendFilterMode == TREND_FILTER_ADX || TrendFilterMode == TREND_FILTER_BOTH)
   {
      adxStrong = IsADXTrendStrong(adxUp);
      if(TrendFilterMode == TREND_FILTER_ADX && (!adxStrong || !adxUp))
         return false; // ADX trend is not strong enough or is down
   }

   // If both filters are enabled, at least one must confirm uptrend
   if(TrendFilterMode == TREND_FILTER_BOTH)
   {
      if(!maTrendUp && (!adxStrong || !adxUp))
         return false; // Neither MA nor ADX confirms uptrend
   }

   return true;
}

//+------------------------------------------------------------------+
// Checks if market conditions are favorable for sell order
//+------------------------------------------------------------------+
bool IsSellConditionMet()
{
   // If trend filtering is disabled, always return true
   if(TrendFilterMode == TREND_FILTER_OFF)
      return true;

   bool adxUp = false;
   bool adxStrong = false;
   bool maTrendUp = false;

   // Check MA trend if enabled
   if(TrendFilterMode == TREND_FILTER_MA || TrendFilterMode == TREND_FILTER_BOTH)
   {
      maTrendUp = IsMATrendUp();
      if(TrendFilterMode == TREND_FILTER_MA && maTrendUp)
         return false; // MA trend is up, don't sell
   }

   // Check ADX trend if enabled
   if(TrendFilterMode == TREND_FILTER_ADX || TrendFilterMode == TREND_FILTER_BOTH)
   {
      adxStrong = IsADXTrendStrong(adxUp);
      if(TrendFilterMode == TREND_FILTER_ADX && (!adxStrong || adxUp))
         return false; // ADX trend is not strong enough or is up
   }

   // If both filters are enabled, at least one must confirm downtrend
   if(TrendFilterMode == TREND_FILTER_BOTH)
   {
      if(maTrendUp && (!adxStrong || adxUp))
         return false; // Neither MA nor ADX confirms downtrend
   }

   return true;
}

//+------------------------------------------------------------------+
// Manage trailing stop loss for active positions
//+------------------------------------------------------------------+
// Function: TrailActivePositionSL
// Description: Manages stop loss levels for all active positions using
//              a prioritized approach:
//              1. First checks for Breakeven Plus conditions
//              2. Then checks for regular trailing stop conditions
//              3. Applies the most favorable stop loss level
//              4. Logs the specific reason for stop loss modifications
// Parameters: None
// Returns: None (modifies position stop losses directly)
//+------------------------------------------------------------------+
void TrailActivePositionSL()
{
   // Get current market prices once for all positions
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

   // Process all positions
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(pos.SelectByIndex(i))
      {
         if(pos.Symbol() == _Symbol && pos.Magic() == MagicNumber)
         {
            ulong ticket = pos.Ticket();
            ENUM_POSITION_TYPE type = pos.PositionType();
            double openPrice = pos.PriceOpen();
            double currentSL = pos.StopLoss();
            double tp = pos.TakeProfit();

            // Skip positions without stop loss
            if(currentSL == 0.0)
               continue;

            // Initialize variables for SL calculation
            double final_sl_to_set = 0.0;
            bool sl_should_be_modified = false;
            string trail_reason = "";
            double trail_offset_value = 0.0;

            // Calculate trailing stop offset based on selected mode
            switch(TrailingSLMode)
            {
               case SL_MODE_POINTS:
                  trail_offset_value = TrailingStopLossActivePoints * point;
                  break;
               case SL_MODE_PERCENTAGE:
                  // Percentage for trailing is based on current price
                  if(type == POSITION_TYPE_BUY)
                     trail_offset_value = bid * (TrailingSLPercentage / 100.0);
                  else
                     trail_offset_value = ask * (TrailingSLPercentage / 100.0);

                  if(trail_offset_value < point)
                     trail_offset_value = point; // Minimum 1 point offset
                  break;
               case SL_MODE_ATR:
                  {
                     double atrVal = GetAtrValue();
                     if(atrHandle == INVALID_HANDLE || atrVal <= (point * 0.1))
                        trail_offset_value = TrailingStopLossActivePoints * point; // Fallback to points
                     else
                        trail_offset_value = atrVal * AtrMultiplier;
                  }
                  break;
               default:
                  trail_offset_value = TrailingStopLossActivePoints * point;
                  break;
            }

            // Apply trailing stop based on position type
            if(type == POSITION_TYPE_BUY)
            {
               // Calculate profit in points
               double profitInPoints = (bid - openPrice) / point;
               double triggerPoints = TrailingSLMode == SL_MODE_POINTS ?
                                     TrailingStopLossActivePoints :
                                     (int)(trail_offset_value / point);

               // STEP 1: Check for Breakeven Plus condition (Primary)
               if(UseBreakevenPlusTrail && profitInPoints >= BreakevenPlusTriggerPoints &&
                  (currentSL < openPrice || currentSL == 0.0))
               {
                  // Calculate potential Breakeven Plus SL
                  double potential_be_plus_sl = NormalizeDouble(openPrice + (BreakevenPlusBufferPoints * point), symbolDigits);

                  // Ensure SL respects broker's stop level
                  double minDistance = (stopLevelPoints + 3) * point;
                  double maxAllowedSL = NormalizeDouble(bid - minDistance, symbolDigits);

                  if(potential_be_plus_sl > maxAllowedSL)
                     potential_be_plus_sl = maxAllowedSL;

                  // Only set if better than current SL
                  if(potential_be_plus_sl > currentSL)
                  {
                     final_sl_to_set = potential_be_plus_sl;
                     sl_should_be_modified = true;
                     trail_reason = " (BE+)";
                  }
               }

               // STEP 2: Check for Regular Trailing condition (Secondary)
               if(profitInPoints > triggerPoints)
               {
                  // Calculate potential regular trailing SL
                  double potential_regular_sl = NormalizeDouble(bid - trail_offset_value, symbolDigits);

                  // Ensure SL respects broker's stop level
                  double minDistance = (stopLevelPoints + 3) * point;
                  double maxAllowedSL = NormalizeDouble(bid - minDistance, symbolDigits);

                  if(potential_regular_sl > maxAllowedSL)
                     potential_regular_sl = maxAllowedSL;

                  // If we already have a BE+ SL to set, only update if trailing is better
                  if(sl_should_be_modified)
                  {
                     if(potential_regular_sl > final_sl_to_set)
                     {
                        final_sl_to_set = potential_regular_sl;
                        trail_reason = " (Trail)";
                     }
                  }
                  // Otherwise, set trailing SL if better than current
                  else if(potential_regular_sl > currentSL)
                  {
                     final_sl_to_set = potential_regular_sl;
                     sl_should_be_modified = true;
                     trail_reason = " (Trail)";
                  }
               }

               // STEP 3: Apply SL modification if needed and meets minimum update threshold
               if(sl_should_be_modified && final_sl_to_set > currentSL && final_sl_to_set != 0.0)
               {
                  // Check if the SL change meets the minimum update threshold
                  bool update_meets_threshold = (currentSL == 0.0) || // Always update if no SL exists
                                               (MathAbs(currentSL - final_sl_to_set) >= MinimumSLUpdatePoints * point);

                  if(update_meets_threshold)
                  {
                     // Set maximum slippage for this critical operation
                     trade.SetDeviationInPoints(MaxSlippagePoints);

                     if(trade.PositionModify(ticket, final_sl_to_set, tp))
                     {
                        // This is important enough to log even without DEBUG
                        Print("SL Update", trail_reason, ": Buy #", ticket, " SL moved from ",
                              DoubleToString(currentSL, symbolDigits), " to ",
                              DoubleToString(final_sl_to_set, symbolDigits));
                     }
                  }
                  else if(DEBUG)
                  {
                     // Log that we skipped the update due to threshold
                     PrintFormat("SL Update skipped: Change too small (%.5f -> %.5f, diff: %.5f, min: %.5f)",
                               currentSL, final_sl_to_set, MathAbs(currentSL - final_sl_to_set), MinimumSLUpdatePoints * point);
                  }
               }
            }
            else if(type == POSITION_TYPE_SELL)
            {
               // Calculate profit in points
               double profitInPoints = (openPrice - ask) / point;
               double triggerPoints = TrailingSLMode == SL_MODE_POINTS ?
                                     TrailingStopLossActivePoints :
                                     (int)(trail_offset_value / point);

               // STEP 1: Check for Breakeven Plus condition (Primary)
               if(UseBreakevenPlusTrail && profitInPoints >= BreakevenPlusTriggerPoints &&
                  (currentSL > openPrice || currentSL == 0.0))
               {
                  // Calculate potential Breakeven Plus SL
                  double potential_be_plus_sl = NormalizeDouble(openPrice - (BreakevenPlusBufferPoints * point), symbolDigits);

                  // Ensure SL respects broker's stop level
                  double minDistance = (stopLevelPoints + 3) * point;
                  double minAllowedSL = NormalizeDouble(ask + minDistance, symbolDigits);

                  if(potential_be_plus_sl < minAllowedSL)
                     potential_be_plus_sl = minAllowedSL;

                  // Only set if better than current SL
                  if(potential_be_plus_sl < currentSL || currentSL == 0.0)
                  {
                     final_sl_to_set = potential_be_plus_sl;
                     sl_should_be_modified = true;
                     trail_reason = " (BE+)";
                  }
               }

               // STEP 2: Check for Regular Trailing condition (Secondary)
               if(profitInPoints > triggerPoints)
               {
                  // Calculate potential regular trailing SL
                  double potential_regular_sl = NormalizeDouble(ask + trail_offset_value, symbolDigits);

                  // Ensure SL respects broker's stop level
                  double minDistance = (stopLevelPoints + 3) * point;
                  double minAllowedSL = NormalizeDouble(ask + minDistance, symbolDigits);

                  if(potential_regular_sl < minAllowedSL)
                     potential_regular_sl = minAllowedSL;

                  // If we already have a BE+ SL to set, only update if trailing is better
                  if(sl_should_be_modified)
                  {
                     if(potential_regular_sl < final_sl_to_set)
                     {
                        final_sl_to_set = potential_regular_sl;
                        trail_reason = " (Trail)";
                     }
                  }
                  // Otherwise, set trailing SL if better than current
                  else if(potential_regular_sl < currentSL)
                  {
                     final_sl_to_set = potential_regular_sl;
                     sl_should_be_modified = true;
                     trail_reason = " (Trail)";
                  }
               }

               // STEP 3: Apply SL modification if needed and meets minimum update threshold
               if(sl_should_be_modified && (final_sl_to_set < currentSL || currentSL == 0.0) && final_sl_to_set != 0.0)
               {
                  // Check if the SL change meets the minimum update threshold
                  bool update_meets_threshold = (currentSL == 0.0) || // Always update if no SL exists
                                               (MathAbs(currentSL - final_sl_to_set) >= MinimumSLUpdatePoints * point);

                  if(update_meets_threshold)
                  {
                     // Set maximum slippage for this critical operation
                     trade.SetDeviationInPoints(MaxSlippagePoints);

                     if(trade.PositionModify(ticket, final_sl_to_set, tp))
                     {
                        // This is important enough to log even without DEBUG
                        Print("SL Update", trail_reason, ": Sell #", ticket, " SL moved from ",
                              DoubleToString(currentSL, symbolDigits), " to ",
                              DoubleToString(final_sl_to_set, symbolDigits));
                     }
                  }
                  else if(DEBUG)
                  {
                     // Log that we skipped the update due to threshold
                     PrintFormat("SL Update skipped: Change too small (%.5f -> %.5f, diff: %.5f, min: %.5f)",
                               currentSL, final_sl_to_set, MathAbs(currentSL - final_sl_to_set), MinimumSLUpdatePoints * point);
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
// Calculate average M1 candle range over the specified period
//+------------------------------------------------------------------+
double GetM1AverageCandleRange(int period)
{
   if(period <= 0)
      return 0.0;

   // Use static array to avoid repeated allocations
   static MqlRates rates[];
   static int last_period = 0;

   // Only resize if needed (different period)
   if(period != last_period)
   {
      ArrayResize(rates, period + 1, 0);
      last_period = period;
   }

   ArraySetAsSeries(rates, true);

   // Get M1 candle data
   int copied = CopyRates(_Symbol, PERIOD_M1, 0, period + 1, rates);

   if(copied <= 0)
   {
      // Only log error if DEBUG is enabled
      if(DEBUG) Print("GetM1AverageCandleRange: Failed to copy M1 rates data. Error: ", GetLastError());
      return 0.0;
   }

   double totalRange = 0.0;

   // Calculate average range (high-low)
   for(int i = 0; i < MathMin(copied, period); i++)
   {
      totalRange += (rates[i].high - rates[i].low);
   }

   double avgRange = totalRange / MathMin(copied, period);

   // Only log if DEBUG is enabled
   if(DEBUG && tick_counter % 100 == 0) // Log only occasionally
      PrintFormat("M1 Average Candle Range: %.5f over %d bars", avgRange, MathMin(copied, period));

   return avgRange;
}

//+------------------------------------------------------------------+
// Check for strong counter candle on M1 timeframe
//+------------------------------------------------------------------+
// Function: CheckM1StrongCounterCandle
// Description: Detects strong counter-momentum candles on M1 timeframe that
//              contradict the direction of a pending order. Used to prevent
//              placing orders against strong short-term momentum.
// Parameters:
//   - orderTypeToCheckAgainst: The order type to check counter-momentum for
//   - candlesToCheck: Number of recent M1 candles to analyze (1-3)
//   - candleSizeMultiplier: Multiplier for average range to detect large candles
//   - avgRangePeriod: Period for calculating average M1 candle range
//   - debug_msg: Reference to string for detailed debug information
// Returns: true if strong counter-momentum is detected, false otherwise
//+------------------------------------------------------------------+
bool CheckM1StrongCounterCandle(ENUM_ORDER_TYPE orderTypeToCheckAgainst, int candlesToCheck, double candleSizeMultiplier, int avgRangePeriod, string& debug_msg)
{
   if(!EnableM1MomentumFilter)
      return false;

   // Update average candle range if needed
   double avgM1Range = GetM1AverageCandleRange(avgRangePeriod);

   // If we don't have a valid range, don't filter
   if(avgM1Range <= 0.0)
   {
      debug_msg = "No valid average range calculated";
      return false;
   }

   // Use static array to avoid repeated allocations
   static MqlRates rates[];
   static int last_candles = 0;

   int candles = MathMin(MathMax(candlesToCheck, 1), 3); // Limit to 1-3 candles

   // Only resize if needed (different number of candles)
   if(candles != last_candles)
   {
      ArrayResize(rates, candles + 1, 0);
      last_candles = candles;
   }

   ArraySetAsSeries(rates, true);

   int copied = CopyRates(_Symbol, PERIOD_M1, 0, candles + 1, rates);

   if(copied <= 0)
   {
      debug_msg = "Failed to copy M1 rates data. Error: " + IntegerToString(GetLastError());
      return false;
   }

   // For Buy Stop orders, check for strong downward momentum
   if(orderTypeToCheckAgainst == ORDER_TYPE_BUY_STOP)
   {
      for(int i = 0; i < MathMin(copied, candles); i++)
      {
         // Check for bearish candle with size larger than threshold
         if(rates[i].close < rates[i].open && // Bearish candle
            (rates[i].open - rates[i].close) > avgM1Range * candleSizeMultiplier) // Large size
         {
            debug_msg = "Bearish M1 candle " + IntegerToString(i) + " with range " +
                       DoubleToString(rates[i].open - rates[i].close, 5) +
                       " > threshold " + DoubleToString(avgM1Range * candleSizeMultiplier, 5);
            return true;
         }
      }
   }
   // For Sell Stop orders, check for strong upward momentum
   else if(orderTypeToCheckAgainst == ORDER_TYPE_SELL_STOP)
   {
      for(int i = 0; i < MathMin(copied, candles); i++)
      {
         // Check for bullish candle with size larger than threshold
         if(rates[i].close > rates[i].open && // Bullish candle
            (rates[i].close - rates[i].open) > avgM1Range * candleSizeMultiplier) // Large size
         {
            debug_msg = "Bullish M1 candle " + IntegerToString(i) + " with range " +
                       DoubleToString(rates[i].close - rates[i].open, 5) +
                       " > threshold " + DoubleToString(avgM1Range * candleSizeMultiplier, 5);
            return true;
         }
      }
   }

   return false; // No strong counter candle detected
}

//+------------------------------------------------------------------+
// Check for counter-momentum on M1 timeframe
//+------------------------------------------------------------------+
bool CheckM1CounterMomentum(ENUM_ORDER_TYPE orderType, const MqlTick &tick)
{
   if(!EnableM1MomentumFilter)
      return false;

   // Check cooldown period
   if(orderType == ORDER_TYPE_BUY_STOP &&
      TimeCurrent() - lastBuyMomentumTime < PeriodSeconds(PERIOD_M1) * M1MomentumFilterCooldownBars)
   {
      if(DEBUG) PrintFormat("Buy momentum filter in cooldown: %d seconds remaining",
                          PeriodSeconds(PERIOD_M1) * M1MomentumFilterCooldownBars - (TimeCurrent() - lastBuyMomentumTime));
      return true; // Still in cooldown period
   }

   if(orderType == ORDER_TYPE_SELL_STOP &&
      TimeCurrent() - lastSellMomentumTime < PeriodSeconds(PERIOD_M1) * M1MomentumFilterCooldownBars)
   {
      if(DEBUG) PrintFormat("Sell momentum filter in cooldown: %d seconds remaining",
                          PeriodSeconds(PERIOD_M1) * M1MomentumFilterCooldownBars - (TimeCurrent() - lastSellMomentumTime));
      return true; // Still in cooldown period
   }

   // Update average candle range if needed
   if(avgM1CandleRange <= 0.0)
      avgM1CandleRange = GetM1AverageCandleRange(M1AvgRangePeriod);

   // If we still don't have a valid range, don't filter
   if(avgM1CandleRange <= 0.0)
      return false;

   // Use static array to avoid repeated allocations
   static MqlRates rates[];
   static int last_candles = 0;

   int candlesToCheck = MathMin(MathMax(M1MomentumCandlesToCheck, 1), 3); // Limit to 1-3 candles

   // Only resize if needed (different number of candles)
   if(candlesToCheck != last_candles)
   {
      ArrayResize(rates, candlesToCheck + 1, 0);
      last_candles = candlesToCheck;
   }

   ArraySetAsSeries(rates, true);

   int copied = CopyRates(_Symbol, PERIOD_M1, 0, candlesToCheck + 1, rates);

   if(copied <= 0)
   {
      if(DEBUG) Print("CheckM1CounterMomentum: Failed to copy M1 rates data. Error: ", GetLastError());
      return false;
   }

   // For Buy Stop orders, check for strong downward momentum
   if(orderType == ORDER_TYPE_BUY_STOP)
   {
      for(int i = 0; i < MathMin(copied, candlesToCheck); i++)
      {
         // Check for bearish candle with size larger than threshold
         if(rates[i].close < rates[i].open && // Bearish candle
            (rates[i].open - rates[i].close) > avgM1CandleRange * M1MomentumCandleSizeMult) // Large size
         {
            if(DEBUG) PrintFormat("M1 Counter-Momentum detected for Buy: Candle %d range %.5f > threshold %.5f",
                                i, rates[i].open - rates[i].close, avgM1CandleRange * M1MomentumCandleSizeMult);
            lastBuyMomentumTime = TimeCurrent();
            return true;
         }
      }
   }
   // For Sell Stop orders, check for strong upward momentum
   else if(orderType == ORDER_TYPE_SELL_STOP)
   {
      for(int i = 0; i < MathMin(copied, candlesToCheck); i++)
      {
         // Check for bullish candle with size larger than threshold
         if(rates[i].close > rates[i].open && // Bullish candle
            (rates[i].close - rates[i].open) > avgM1CandleRange * M1MomentumCandleSizeMult) // Large size
         {
            if(DEBUG) PrintFormat("M1 Counter-Momentum detected for Sell: Candle %d range %.5f > threshold %.5f",
                                i, rates[i].close - rates[i].open, avgM1CandleRange * M1MomentumCandleSizeMult);
            lastSellMomentumTime = TimeCurrent();
            return true;
         }
      }
   }

   return false; // No counter-momentum detected
}

//+------------------------------------------------------------------+
// Update volatility status based on ATR
//+------------------------------------------------------------------+
// Function: UpdateVolatilityStatus
// Description: Analyzes current market volatility using ATR indicator
//              and compares it to a moving average of ATR values.
//              Sets g_isMarketTooVolatile flag when volatility exceeds
//              the threshold, which prevents new order placement.
// Parameters: None
// Returns: None (updates global volatility state)
//+------------------------------------------------------------------+
void UpdateVolatilityStatus()
{
   if(!EnableVolatilityFilter || g_volatility_atr_handle == INVALID_HANDLE)
      return;

   // Use static array to avoid repeated allocations
   static double atrBuffer[];
   static int last_period = 0;

   // Only resize if needed (different period)
   if(VolatilityATR_MAPeriod != last_period)
   {
      ArrayResize(atrBuffer, VolatilityATR_MAPeriod + 1, 0);
      last_period = VolatilityATR_MAPeriod;
   }

   ArraySetAsSeries(atrBuffer, true);

   // Get current ATR value
   int copied = CopyBuffer(g_volatility_atr_handle, 0, 0, VolatilityATR_MAPeriod + 1, atrBuffer);

   if(copied <= 0)
   {
      if(DEBUG) Print("UpdateVolatilityStatus: Failed to copy ATR data. Error: ", GetLastError());
      return;
   }

   // Calculate ATR moving average
   double atrSum = 0.0;
   for(int i = 1; i <= VolatilityATR_MAPeriod; i++) // Start from 1 to skip current incomplete bar
   {
      if(i < copied)
         atrSum += atrBuffer[i];
   }

   atrMA = atrSum / MathMin(VolatilityATR_MAPeriod, copied - 1);
   currentATR = atrBuffer[0]; // Current ATR

   // Check if current ATR exceeds threshold
   bool previousVolatilityState = g_isMarketTooVolatile;
   g_isMarketTooVolatile = (currentATR > atrMA * VolatilityATR_ThresholdMult);

   // Log state change
   if(previousVolatilityState != g_isMarketTooVolatile && DEBUG)
   {
      if(g_isMarketTooVolatile)
         PrintFormat("Volatility Filter: HIGH VOLATILITY detected. Current ATR: %.5f, Average: %.5f, Ratio: %.2f",
                    currentATR, atrMA, currentATR / atrMA);
      else
         PrintFormat("Volatility Filter: Volatility returned to normal. Current ATR: %.5f, Average: %.5f, Ratio: %.2f",
                    currentATR, atrMA, currentATR / atrMA);
   }
}

//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result)
{
   // Start performance measurement
   ulong start_time = GetMicrosecondCount();

   // Filter out irrelevant transaction types early to improve performance
   if(trans.type != TRADE_TRANSACTION_ORDER_ADD &&
      trans.type != TRADE_TRANSACTION_ORDER_DELETE &&
      trans.type != TRADE_TRANSACTION_HISTORY_ADD &&
      trans.type != TRADE_TRANSACTION_DEAL_ADD)
   {
      // Skip processing irrelevant transaction types
      return;
   }

   // Set the transaction processing flag to prevent concurrent operations
   isProcessingTransaction = true;

   // If using state machine, update the state
   if(StateManagement == STATE_MACHINE)
   {
      currentState = STATE_PROCESSING_TRANSACTION;
      stateChangeTime = TimeCurrent();
   }

   // Log transaction details for debugging - only if DEBUG is enabled
   if(DEBUG)
   {
      Print("OnTradeTransaction: Type=", EnumToString(trans.type), ", Order=", trans.order,
            ", Position=", trans.position, ", Deal=", trans.deal);
   }

   // Optimized relevance check - first check magic number in request (fastest)
   bool isRelevant = false;

   // Check if request has our magic number
   if(request.magic == MagicNumber)
   {
      isRelevant = true;
   }
   // Check if it's one of our pending orders (fast check using cached values)
   else if(trans.order != 0 && (trans.order == (ulong)buyStopTicket || trans.order == (ulong)sellStopTicket))
   {
      isRelevant = true;
   }
   // Check if position belongs to this EA (requires database lookup)
   else if(trans.position != 0 && pos.SelectByTicket(trans.position) && pos.Magic() == MagicNumber)
   {
      isRelevant = true;
   }
   // Fallback: check if order has our magic number (slowest check)
   else if(trans.order != 0 && ord.Select((ulong)trans.order) && ord.Magic() == MagicNumber)
   {
      isRelevant = true;
   }

   if(!isRelevant)
   {
      // Reset flags before returning
      isProcessingTransaction = false;
      if(StateManagement == STATE_MACHINE) currentState = STATE_NORMAL;

      // Log performance for irrelevant transactions
      if(DEBUG)
      {
         ulong execution_time = GetMicrosecondCount() - start_time;
         if(execution_time > 5000) // Only log if it took more than 5ms
         {
            PrintFormat("OnTradeTransaction: Irrelevant transaction processed in %d μs", execution_time);
         }
      }

      return;
   }

   switch(trans.type)
   {
      case TRADE_TRANSACTION_DEAL_ADD: // A deal was added (position opened or modified by deal)
         // Check if this deal corresponds to one of our pending stop orders being triggered
         if(trans.order_type == ORDER_TYPE_BUY_STOP && trans.order == (ulong)buyStopTicket)
         {
            // This is important enough to log even without DEBUG
            Print("OnTradeTransaction: Buy Stop #", buyStopTicket, " triggered. Deal #", trans.deal,
                  ", Pos #", trans.position, ". Setting Initial SL.");

            // Set initial stop loss for the new position
            SetInitialStopLoss(trans.position, POSITION_TYPE_BUY, latestTick);

            // Store the last state before resetting
            OrderInfo lastBuyInfo = buyStopInfo;

            // Reset ticket as order is filled
            buyStopTicket = 0;
            buyStopInfo.ticket = 0;

            if(DEBUG) Print("Buy Stop #", lastBuyInfo.ticket, " filled and reset. Last state: ",
                           EnumToString(lastBuyInfo.lastKnownState),
                           ", Created: ", TimeToString(lastBuyInfo.creationTime),
                           ", Age: ", TimeCurrent() - lastBuyInfo.creationTime, " seconds");
         }
         else if(trans.order_type == ORDER_TYPE_SELL_STOP && trans.order == (ulong)sellStopTicket)
         {
            // This is important enough to log even without DEBUG
            Print("OnTradeTransaction: Sell Stop #", sellStopTicket, " triggered. Deal #", trans.deal,
                  ", Pos #", trans.position, ". Setting Initial SL.");

            // Set initial stop loss for the new position
            SetInitialStopLoss(trans.position, POSITION_TYPE_SELL, latestTick);

            // Store the last state before resetting
            OrderInfo lastSellInfo = sellStopInfo;

            // Reset ticket
            sellStopTicket = 0;
            sellStopInfo.ticket = 0;

            if(DEBUG) Print("Sell Stop #", lastSellInfo.ticket, " filled and reset. Last state: ",
                           EnumToString(lastSellInfo.lastKnownState),
                           ", Created: ", TimeToString(lastSellInfo.creationTime),
                           ", Age: ", TimeCurrent() - lastSellInfo.creationTime, " seconds");
         }
         // Check for any other positions that might have been opened with our magic number
         else if(trans.position != 0)
         {
            if(pos.SelectByTicket(trans.position) && pos.Magic() == MagicNumber)
            {
               // If this is a new position and it doesn't have a stop loss
               if(pos.StopLoss() == 0.0)
               {
                  // This is important enough to log even without DEBUG
                  Print("New position #", trans.position, " (", EnumToString(pos.PositionType()),
                        ") detected without SL. Setting initial SL.");

                  // Set initial stop loss
                  SetInitialStopLoss(trans.position, pos.PositionType(), latestTick);
               }
            }
         }
         break;

      case TRADE_TRANSACTION_ORDER_DELETE: // Pending order deleted
         if(trans.order == (ulong)buyStopTicket)
         {
            // Only log if it wasn't deleted due to being filled (TRADE_RETCODE_DONE/DONE_PARTIAL means filled)
            if(result.retcode != TRADE_RETCODE_DONE && result.retcode != TRADE_RETCODE_DONE_PARTIAL && result.retcode != TRADE_RETCODE_NO_CHANGES)
            {
                 if(DEBUG) Print("OnTradeTransaction: Buy Stop #", buyStopTicket,
                                " deleted (reason other than fill). Result: ", result.retcode,
                                " - ", result.comment);
            }

            // Store the last state before resetting
            OrderInfo lastBuyInfo = buyStopInfo;

            // Reset ticket
            buyStopTicket = 0;
            buyStopInfo.ticket = 0;

            if(DEBUG) Print("Buy Stop #", lastBuyInfo.ticket, " deleted. Last state: ",
                           EnumToString(lastBuyInfo.lastKnownState),
                           ", Created: ", TimeToString(lastBuyInfo.creationTime),
                           ", Age: ", TimeCurrent() - lastBuyInfo.creationTime, " seconds");
         }
         else if(trans.order == (ulong)sellStopTicket)
         {
            if(result.retcode != TRADE_RETCODE_DONE && result.retcode != TRADE_RETCODE_DONE_PARTIAL && result.retcode != TRADE_RETCODE_NO_CHANGES)
            {
                if(DEBUG) Print("OnTradeTransaction: Sell Stop #", sellStopTicket,
                               " deleted (reason other than fill). Result: ", result.retcode,
                               " - ", result.comment);
            }

            // Store the last state before resetting
            OrderInfo lastSellInfo = sellStopInfo;

            // Reset ticket
            sellStopTicket = 0;
            sellStopInfo.ticket = 0;

            if(DEBUG) Print("Sell Stop #", lastSellInfo.ticket, " deleted. Last state: ",
                           EnumToString(lastSellInfo.lastKnownState),
                           ", Created: ", TimeToString(lastSellInfo.creationTime),
                           ", Age: ", TimeCurrent() - lastSellInfo.creationTime, " seconds");
         }
         break;
   }

   // Reset the transaction processing flag
   isProcessingTransaction = false;

   // If using state machine, return to normal state
   if(StateManagement == STATE_MACHINE)
   {
      currentState = STATE_NORMAL;
      if(DEBUG) Print("Transaction processing complete - returning to STATE_NORMAL");
   }

   // Log performance for transaction processing
   if(DEBUG)
   {
      ulong execution_time = GetMicrosecondCount() - start_time;
      if(execution_time > 10000) // Only log if it took more than 10ms
      {
         PrintFormat("OnTradeTransaction: Transaction type %s processed in %d μs",
                    EnumToString(trans.type), execution_time);
      }
   }
}
//+------------------------------------------------------------------+
