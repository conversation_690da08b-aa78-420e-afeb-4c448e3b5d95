//+------------------------------------------------------------------+
//|                                HFT_DynamicChannelRider_v1.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

// Include necessary libraries
#include <Trade/Trade.mqh>
#include <Trade/SymbolInfo.mqh>
#include <Trade/PositionInfo.mqh>
#include <Trade/OrderInfo.mqh>

// Object instances
CTrade trade;
CSymbolInfo symbolInfo;
CPositionInfo positionInfo;
COrderInfo orderInfo;

//--- Enumerations for input parameters
enum ENUM_TRAIL_ACTIVATION_METHOD {
   ATR_MULT = 0,   // ATR Multiplier
   COST_MULT = 1   // Cost Multiplier
};

//--- Global Settings & Inputs
input group "===Global Settings==="
input int MagicNumber = 20240520;                // Unique identifier for this EA's orders
input string OrderComment = "HFT_DCR_v1";        // Comment for orders
input double Lots = 0.1;                         // Trade volume per order
input int MaxAcceptableSpreadPoints = 150;       // Maximum live spread (in points) allowed for trading
input double CommissionPerStdLot_Currency = 7.0; // Commission charged by the broker per standard lot for a round turn
input int EstimatedTypicalRawSpreadPoints = 110; // User's observed typical raw spread for the instrument in points
input double RiskManagement_PercentPerTrade = 0.5; // Percentage of account equity to risk per trade (0 = use fixed lots)
input int StopLevelBufferPoints = 5;             // Additional points buffer to add to broker's SYMBOL_TRADE_STOPS_LEVEL

//--- Session & News Filtering
input group "===Session Filters==="
input bool EnableSessionFilter = true;           // Enable session filter
input int Session1_StartHour = 7;                // Start hour (GMT) for trading session 1
input int Session1_StartMinute = 0;              // Start minute for trading session 1
input int Session1_EndHour = 9;                  // End hour (GMT) for trading session 1
input int Session1_EndMinute = 0;                // End minute for trading session 1
input bool Session2_Enable = true;               // Enable second trading session
input int Session2_StartHour = 13;               // Start hour (GMT) for trading session 2
input int Session2_StartMinute = 30;             // Start minute for trading session 2
input int Session2_EndHour = 17;                 // End hour (GMT) for trading session 2
input int Session2_EndMinute = 0;                // End minute for trading session 2

input group "===News Filter==="
input bool EnableNewsFilter = true;              // Enable news filter
input int MinutesBeforeNewsToPause = 20;         // Minutes before news to pause trading
input int MinutesAfterNewsToPause = 20;          // Minutes after news to pause trading
// For simplicity, we'll implement a basic news filter. For a more advanced solution,
// an external news data feed integration would be required.

//--- Overall Market Volatility Filter
input group "===Overall Volatility Filter==="
input bool EnableOverallVolatilityFilter = true; // Enable overall volatility filter
input ENUM_TIMEFRAMES HigherTF_ATR_Timeframe = PERIOD_M15; // Timeframe for the higher timeframe ATR
input int HigherTF_ATR_Period = 14;              // Period for higher timeframe ATR
input double MinOverallVolatilityATR_Points = 250; // Minimum HigherTF ATR value (in points) required to trade
input double MaxOverallVolatilityATR_Points = 2000; // Maximum HigherTF ATR value (in points) allowed for trading

//--- Indicator Settings (M1 Based)
input group "===Indicators (M1 Based)==="
input int M1_ATR_Period = 14;                    // ATR period for M1 timeframe
input int M1_VolumeSMA_Period = 14;              // SMA period for volume on M1 timeframe

//--- Dynamic Channel/Range Identification
input group "===Channel Identification==="
input int Channel_Bars_Lookback = 7;             // Number of past M1 bars to define channel
input double Min_Channel_ATR_Factor = 0.4;       // Minimum channel width relative to current M1 ATR
input double Min_Channel_Absolute_Points_Factor_Of_Cost = 0.75; // Channel width must be at least (TotalCostPerTradePoints * this factor)
input double Max_Channel_ATR_Factor = 1.8;       // Maximum channel width relative to current M1 ATR

//--- Breakout Entry Setup & Trigger
input group "===Entry Setup==="
input double Breakout_Offset_ATR_Mult = 0.25;    // Pending order offset from channel edge, as multiplier of M1 ATR
input double Initial_SL_Buffer_ATR_Mult = 0.25;  // SL buffer from opposite channel edge, as multiplier of M1 ATR
input double Min_RiskReward_SL_Factor = 1.1;     // Initial SL distance must be at least (TotalCostPerTradePoints * this factor)
input bool TickRate_Surge_Enable = true;         // Enable tick rate surge confirmation
input int TickRate_LookbackSeconds_ForAvg = 60;  // Seconds to look back to calculate average ticks per second
input double TickRate_Surge_Factor = 1.8;        // Current ticks/sec must be this factor * average to confirm surge
input bool Volume_Surge_Enable = true;           // Enable volume surge confirmation
input double Volume_Surge_Factor = 1.8;          // Triggering bar's volume must be this factor * M1 Volume SMA to confirm
input int Order_Expiry_Seconds = 90;             // Pending orders expire if not filled within this time

//--- Active Trade Management
input group "===Trade Management==="
input ENUM_TRAIL_ACTIVATION_METHOD Trail_Activation_Method = COST_MULT; // How to determine trailing stop activation profit
input double Trail_Activation_ATR_Mult = 1.0;    // Activates trailing if profit > M1_ATR_at_entry * this multiplier
input double Trail_Activation_Cost_Mult = 1.75;  // Activates trailing if profit > TotalCostPerTradePoints * this multiplier
input int BE_Plus_LockIn_Points = 20;            // Additional points locked in beyond TotalCost when BE+ activates
input double Trail_Stop_Distance_ATR_Mult = 0.6; // Trailing stop distance from best price, as multiplier of M1_ATR_at_entry

input bool Enable_Fixed_TP = false;              // Enable fixed take profit
input double Fixed_TP_ATR_Mult = 2.0;            // TP target from entry, as multiplier of M1_ATR_at_entry

input int Max_Hold_M1_Bars = 5;                  // Max M1 bars to hold a trade if not performing
input double Profit_Target_For_MaxHold_Bypass_Cost_Mult = 2.5; // If trade profit > (TotalCostPerTradePoints * this factor), Max_Hold_M1_Bars rule is bypassed
input bool Stall_Exit_Enable = true;             // Enable stall exit
input int Stall_Lookback_Seconds = 60;           // If no new high/low for this many seconds while in profit
input double Stall_Tighten_Trail_ATR_Mult = 0.15; // Aggressively tighten trail to this M1_ATR_at_entry multiplier if stalled

//--- Post-Trade
input group "===Post-Trade==="
input int Global_Trade_Cooldown_M1_Bars = 3;     // M1 bars to wait after any trade closes before looking for new setups

//--- Debug Mode
input group "===Debug Settings==="
input bool DEBUG_MODE = false;                   // Enable detailed debug logging

//--- Indicator handles
int handle_M1_ATR;
int handle_M1_VolumeSMA;
int handle_HigherTF_ATR;

//--- Global variables
double g_current_M1_ATR_value = 0.0;
double g_current_M1_ATR_points = 0.0;
double g_M1_VolumeSMA_value = 0.0;
double g_HigherTF_ATR_value_points = 0.0;
double g_total_cost_per_trade_points = 0.0;
double g_commission_per_trade_points_current_lot = 0.0;
bool g_is_trading_allowed_by_filters = false;
datetime g_last_trade_close_time = 0;
ulong g_active_trade_ticket = 0;
double g_active_trade_entry_price = 0.0;
bool g_active_trade_is_buy = false;
double g_active_trade_initial_SL_price = 0.0;
double g_active_trade_ATR_at_entry_points = 0.0;
bool g_active_trade_trailing_activated = false;
double g_active_trade_current_trail_SL = 0.0;
ulong g_pending_buy_stop_ticket = 0;
ulong g_pending_sell_stop_ticket = 0;
datetime g_pending_order_placement_time = 0;

// State machine for the EA
enum ENUM_EA_STATE {
   IDLE,
   AWAITING_CHANNEL,
   PENDING_ORDERS_PLACED,
   IN_TRADE_BUY,
   IN_TRADE_SELL,
   COOLDOWN
};

ENUM_EA_STATE g_ea_state = IDLE;

// Tick data collection for tick rate calculation
datetime g_tick_times[];
int g_current_tick_index = 0;
int g_tick_count = 0;
double g_avg_ticks_per_second = 0.0;
datetime g_last_tick_rate_calc_time = 0;
datetime g_last_stall_check_time = 0;
double g_best_price_since_entry = 0.0;
datetime g_last_new_best_price_time = 0;
int g_bars_in_current_trade = 0;
datetime g_last_volatility_check_time = 0;

// Function prototypes
void CalculateCommissionInPoints();
bool IsTradingAllowedBySession();
bool IsTradingAllowedByNews();
bool IsTradingAllowedByVolatility();
bool IsTradingAllowedBySpread();
bool IsTradingAllowedByCooldown();
void CalculateAverageTickRate();
void UpdateIndicators();
bool IdentifyDynamicChannel(double &channel_high, double &channel_low, double &channel_width_points);
void SetupBreakoutEntry(double channel_high, double channel_low, double channel_width_points);
void ManageOpenPosition();
void CheckPendingOrdersExpiry();
bool IsPreTriggerConfirmationMet();

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize the trade object
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(_Symbol);
   trade.SetDeviationInPoints(10); // Default deviation for slippage
   trade.SetAsyncMode(true);       // Use async mode for faster execution

   // Initialize the symbol info object
   if(!symbolInfo.Name(_Symbol)) {
      Print("Failed to initialize symbol info for ", _Symbol);
      return INIT_FAILED;
   }

   // Initialize indicators
   handle_M1_ATR = iATR(_Symbol, PERIOD_M1, M1_ATR_Period);
   handle_M1_VolumeSMA = iMA(_Symbol, PERIOD_M1, M1_VolumeSMA_Period, 0, MODE_SMA, VOLUME_REAL);
   handle_HigherTF_ATR = iATR(_Symbol, HigherTF_ATR_Timeframe, HigherTF_ATR_Period);

   if(handle_M1_ATR == INVALID_HANDLE || handle_M1_VolumeSMA == INVALID_HANDLE || handle_HigherTF_ATR == INVALID_HANDLE) {
      Print("Failed to initialize indicators");
      return INIT_FAILED;
   }

   // Calculate commission in points
   CalculateCommissionInPoints();

   // Calculate total cost per trade in points
   g_total_cost_per_trade_points = EstimatedTypicalRawSpreadPoints + g_commission_per_trade_points_current_lot;

   // Initialize tick data collection array
   ArrayResize(g_tick_times, TickRate_LookbackSeconds_ForAvg * 100); // Allocate enough space for high tick rates

   if(DEBUG_MODE) {
      Print("HFT_DynamicChannelRider_v1 initialized successfully");
      Print("Total cost per trade (points): ", g_total_cost_per_trade_points);
      Print("Commission per trade (points): ", g_commission_per_trade_points_current_lot);
   }

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Calculate commission in points                                   |
//+------------------------------------------------------------------+
void CalculateCommissionInPoints()
{
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double pointValue = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

   // Calculate the value of 1 point in the account currency
   double pointCost = tickValue * (pointValue / tickSize);

   // Calculate commission per standard lot in points
   double commissionPerStdLotPoints = CommissionPerStdLot_Currency / pointCost;

   // Calculate commission for current lot size in points (round trip)
   g_commission_per_trade_points_current_lot = commissionPerStdLotPoints * (Lots / 1.0);

   if(DEBUG_MODE) {
      Print("Point value in account currency: ", pointCost);
      Print("Commission per standard lot (points): ", commissionPerStdLotPoints);
      Print("Commission for current lot size (points): ", g_commission_per_trade_points_current_lot);
   }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Clean up indicator handles
   if(handle_M1_ATR != INVALID_HANDLE)
      IndicatorRelease(handle_M1_ATR);

   if(handle_M1_VolumeSMA != INVALID_HANDLE)
      IndicatorRelease(handle_M1_VolumeSMA);

   if(handle_HigherTF_ATR != INVALID_HANDLE)
      IndicatorRelease(handle_HigherTF_ATR);

   // Print deinitialization reason if in debug mode
   if(DEBUG_MODE) {
      string reason_text = "";
      switch(reason) {
         case REASON_PROGRAM:     reason_text = "Program"; break;
         case REASON_REMOVE:      reason_text = "Removed from chart"; break;
         case REASON_RECOMPILE:   reason_text = "Recompiled"; break;
         case REASON_CHARTCHANGE: reason_text = "Chart symbol or period changed"; break;
         case REASON_CHARTCLOSE:  reason_text = "Chart closed"; break;
         case REASON_PARAMETERS:  reason_text = "Parameters changed"; break;
         case REASON_ACCOUNT:     reason_text = "Account changed"; break;
         case REASON_TEMPLATE:    reason_text = "Template applied"; break;
         case REASON_INITFAILED:  reason_text = "OnInit() failed"; break;
         case REASON_CLOSE:       reason_text = "Terminal closed"; break;
         default:                 reason_text = "Unknown reason"; break;
      }
      Print("HFT_DynamicChannelRider_v1 deinitialized. Reason: ", reason_text);
   }
}

//+------------------------------------------------------------------+
//| Check if trading is allowed by session filter                    |
//+------------------------------------------------------------------+
bool IsTradingAllowedBySession()
{
   if(!EnableSessionFilter)
      return true;

   MqlDateTime current_time;
   TimeToStruct(TimeCurrent(), current_time);

   int current_hour = current_time.hour;
   int current_minute = current_time.min;
   int current_time_minutes = current_hour * 60 + current_minute;

   // Session 1 check
   int session1_start_minutes = Session1_StartHour * 60 + Session1_StartMinute;
   int session1_end_minutes = Session1_EndHour * 60 + Session1_EndMinute;

   bool in_session1 = (current_time_minutes >= session1_start_minutes && current_time_minutes < session1_end_minutes);

   // Session 2 check
   bool in_session2 = false;
   if(Session2_Enable) {
      int session2_start_minutes = Session2_StartHour * 60 + Session2_StartMinute;
      int session2_end_minutes = Session2_EndHour * 60 + Session2_EndMinute;
      in_session2 = (current_time_minutes >= session2_start_minutes && current_time_minutes < session2_end_minutes);
   }

   return (in_session1 || in_session2);
}

//+------------------------------------------------------------------+
//| Check if trading is allowed by news filter                       |
//+------------------------------------------------------------------+
bool IsTradingAllowedByNews()
{
   if(!EnableNewsFilter)
      return true;

   // For a complete implementation, you would need to integrate with an external news data feed
   // or implement a user-input based news calendar

   // This is a simplified placeholder implementation
   // In a real implementation, you would check if the current time is within
   // MinutesBeforeNewsToPause or MinutesAfterNewsToPause of any high-impact news event

   // For now, we'll just return true
   return true;
}

//+------------------------------------------------------------------+
//| Check if trading is allowed by volatility filter                 |
//+------------------------------------------------------------------+
bool IsTradingAllowedByVolatility()
{
   if(!EnableOverallVolatilityFilter)
      return true;

   // Only update volatility check every 5 minutes to reduce processing load
   datetime current_time = TimeCurrent();
   if(current_time - g_last_volatility_check_time < 300) // 5 minutes = 300 seconds
      return g_is_trading_allowed_by_filters;

   g_last_volatility_check_time = current_time;

   // Get the current higher timeframe ATR value
   double atr_buffer[];
   if(CopyBuffer(handle_HigherTF_ATR, 0, 0, 1, atr_buffer) <= 0) {
      if(DEBUG_MODE)
         Print("Failed to get higher timeframe ATR value");
      return false;
   }

   // Convert ATR to points
   g_HigherTF_ATR_value_points = atr_buffer[0] / _Point;

   // Check if ATR is within acceptable range
   bool volatility_ok = (g_HigherTF_ATR_value_points >= MinOverallVolatilityATR_Points &&
                         g_HigherTF_ATR_value_points <= MaxOverallVolatilityATR_Points);

   if(DEBUG_MODE && !volatility_ok) {
      Print("Volatility filter: ATR = ", g_HigherTF_ATR_value_points, " points, outside range [",
            MinOverallVolatilityATR_Points, ", ", MaxOverallVolatilityATR_Points, "]");
   }

   return volatility_ok;
}

//+------------------------------------------------------------------+
//| Check if trading is allowed by spread filter                     |
//+------------------------------------------------------------------+
bool IsTradingAllowedBySpread()
{
   int current_spread = (int)SymbolInfoInteger(_Symbol, SYMBOL_SPREAD);

   bool spread_ok = (current_spread <= MaxAcceptableSpreadPoints);

   if(DEBUG_MODE && !spread_ok) {
      Print("Spread filter: Current spread = ", current_spread, " points, max allowed = ", MaxAcceptableSpreadPoints);
   }

   return spread_ok;
}

//+------------------------------------------------------------------+
//| Check if trading is allowed by cooldown period                   |
//+------------------------------------------------------------------+
bool IsTradingAllowedByCooldown()
{
   if(g_last_trade_close_time == 0)
      return true;

   int bars_since_last_trade = iBarShift(_Symbol, PERIOD_M1, g_last_trade_close_time, false);

   bool cooldown_ok = (bars_since_last_trade >= Global_Trade_Cooldown_M1_Bars);

   if(DEBUG_MODE && !cooldown_ok) {
      Print("Cooldown filter: Bars since last trade = ", bars_since_last_trade,
            ", cooldown required = ", Global_Trade_Cooldown_M1_Bars);
   }

   return cooldown_ok;
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Record tick time for tick rate calculation
   if(TickRate_Surge_Enable) {
      g_tick_times[g_current_tick_index] = TimeCurrent();
      g_current_tick_index = (g_current_tick_index + 1) % ArraySize(g_tick_times);
      g_tick_count++;

      // Calculate average ticks per second every 5 seconds
      datetime current_time = TimeCurrent();
      if(current_time - g_last_tick_rate_calc_time >= 5) { // 5 seconds
         g_last_tick_rate_calc_time = current_time;
         CalculateAverageTickRate();
      }
   }

   // 1. Pre-Flight Checks
   UpdateIndicators();

   // Check if trading is allowed by all filters
   g_is_trading_allowed_by_filters = IsTradingAllowedBySession() &&
                                    IsTradingAllowedByNews() &&
                                    IsTradingAllowedBySpread() &&
                                    IsTradingAllowedByVolatility() &&
                                    IsTradingAllowedByCooldown();

   if(!g_is_trading_allowed_by_filters) {
      if(DEBUG_MODE)
         Print("Trading not allowed by filters");
      return;
   }

   // If we have an active trade, manage it
   if(g_active_trade_ticket > 0) {
      ManageOpenPosition();
      return;
   }

   // If we have pending orders, check if they need to be expired
   if(g_pending_buy_stop_ticket > 0 || g_pending_sell_stop_ticket > 0) {
      CheckPendingOrdersExpiry();
      return;
   }

   // If we're in cooldown state, check if we can exit cooldown
   if(g_ea_state == COOLDOWN) {
      if(IsTradingAllowedByCooldown()) {
         g_ea_state = IDLE;
         if(DEBUG_MODE)
            Print("Exiting cooldown state");
      } else {
         return;
      }
   }

   // 2. Identify Dynamic Channel
   double channel_high, channel_low, channel_width_points;
   if(!IdentifyDynamicChannel(channel_high, channel_low, channel_width_points)) {
      if(DEBUG_MODE)
         Print("No valid channel identified");
      return;
   }

   // 3. Setup Breakout Entry
   SetupBreakoutEntry(channel_high, channel_low, channel_width_points);
}

//+------------------------------------------------------------------+
//| Calculate average tick rate                                      |
//+------------------------------------------------------------------+
void CalculateAverageTickRate()
{
   if(g_tick_count < 10) // Need at least 10 ticks to calculate a meaningful average
      return;

   datetime current_time = TimeCurrent();
   int count = 0;
   int seconds_to_look_back = MathMin(TickRate_LookbackSeconds_ForAvg, g_tick_count);

   for(int i = 0; i < ArraySize(g_tick_times); i++) {
      if(g_tick_times[i] > 0 && current_time - g_tick_times[i] <= seconds_to_look_back)
         count++;
   }

   g_avg_ticks_per_second = (double)count / seconds_to_look_back;

   if(DEBUG_MODE)
      Print("Average tick rate: ", g_avg_ticks_per_second, " ticks/second");
}

//+------------------------------------------------------------------+
//| Update indicator values                                          |
//+------------------------------------------------------------------+
void UpdateIndicators()
{
   // Update M1 ATR
   double atr_buffer[];
   if(CopyBuffer(handle_M1_ATR, 0, 0, 1, atr_buffer) > 0) {
      g_current_M1_ATR_value = atr_buffer[0];
      g_current_M1_ATR_points = g_current_M1_ATR_value / _Point;
   }

   // Update M1 Volume SMA
   double volume_buffer[];
   if(CopyBuffer(handle_M1_VolumeSMA, 0, 0, 1, volume_buffer) > 0) {
      g_M1_VolumeSMA_value = volume_buffer[0];
   }

   if(DEBUG_MODE) {
      Print("M1 ATR: ", g_current_M1_ATR_value, " (", g_current_M1_ATR_points, " points)");
      Print("M1 Volume SMA: ", g_M1_VolumeSMA_value);
   }
}

//+------------------------------------------------------------------+
//| Identify dynamic channel                                         |
//+------------------------------------------------------------------+
bool IdentifyDynamicChannel(double &channel_high, double &channel_low, double &channel_width_points)
{
   // Initialize variables
   channel_high = 0.0;
   channel_low = DBL_MAX;

   // Get high/low prices for the lookback period
   double highs[], lows[];
   if(CopyHigh(_Symbol, PERIOD_M1, 0, Channel_Bars_Lookback, highs) <= 0 ||
      CopyLow(_Symbol, PERIOD_M1, 0, Channel_Bars_Lookback, lows) <= 0) {
      if(DEBUG_MODE)
         Print("Failed to get price data for channel identification");
      return false;
   }

   // Find highest high and lowest low
   ArraySetAsSeries(highs, true);
   ArraySetAsSeries(lows, true);

   for(int i = 0; i < Channel_Bars_Lookback; i++) {
      if(highs[i] > channel_high)
         channel_high = highs[i];

      if(lows[i] < channel_low)
         channel_low = lows[i];
   }

   // Calculate channel width in points
   channel_width_points = (channel_high - channel_low) / _Point;

   // Validate channel width
   bool min_width_ok = (channel_width_points >= Min_Channel_ATR_Factor * g_current_M1_ATR_points);
   bool max_width_ok = (channel_width_points <= Max_Channel_ATR_Factor * g_current_M1_ATR_points);
   bool cost_factor_ok = (channel_width_points >= Min_Channel_Absolute_Points_Factor_Of_Cost * g_total_cost_per_trade_points);

   bool channel_valid = min_width_ok && max_width_ok && cost_factor_ok;

   if(DEBUG_MODE) {
      Print("Channel high: ", channel_high, ", Channel low: ", channel_low);
      Print("Channel width: ", channel_width_points, " points");
      Print("Min width required: ", Min_Channel_ATR_Factor * g_current_M1_ATR_points, " points");
      Print("Max width allowed: ", Max_Channel_ATR_Factor * g_current_M1_ATR_points, " points");
      Print("Min width based on cost: ", Min_Channel_Absolute_Points_Factor_Of_Cost * g_total_cost_per_trade_points, " points");
      Print("Channel valid: ", channel_valid);
   }

   return channel_valid;
}

//+------------------------------------------------------------------+
//| Setup breakout entry orders                                      |
//+------------------------------------------------------------------+
void SetupBreakoutEntry(double channel_high, double channel_low, double channel_width_points)
{
   // Get fresh tick data
   MqlTick latest_tick;
   if(!SymbolInfoTick(_Symbol, latest_tick)) {
      if(DEBUG_MODE)
         Print("Failed to get latest tick data");
      return;
   }

   // Get minimum stop level in points
   int stop_level = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) + StopLevelBufferPoints;

   // Calculate breakout levels with offset
   double breakout_offset = Breakout_Offset_ATR_Mult * g_current_M1_ATR_points * _Point;
   double buy_stop_level = channel_high + breakout_offset;
   double sell_stop_level = channel_low - breakout_offset;

   // Ensure levels respect minimum stop level
   if(buy_stop_level < latest_tick.ask + stop_level * _Point)
      buy_stop_level = latest_tick.ask + stop_level * _Point;

   if(sell_stop_level > latest_tick.bid - stop_level * _Point)
      sell_stop_level = latest_tick.bid - stop_level * _Point;

   // Calculate stop loss levels
   double sl_buffer = Initial_SL_Buffer_ATR_Mult * g_current_M1_ATR_points * _Point;
   double buy_sl_price = channel_low - sl_buffer;
   double sell_sl_price = channel_high + sl_buffer;

   // Ensure SL respects minimum risk/reward based on cost
   double buy_sl_distance_points = (buy_stop_level - buy_sl_price) / _Point;
   double sell_sl_distance_points = (sell_sl_price - sell_stop_level) / _Point;

   double min_sl_distance_points = Min_RiskReward_SL_Factor * g_total_cost_per_trade_points;

   if(buy_sl_distance_points < min_sl_distance_points) {
      buy_sl_price = buy_stop_level - min_sl_distance_points * _Point;
      if(DEBUG_MODE)
         Print("Adjusted buy SL to meet minimum risk/reward");
   }

   if(sell_sl_distance_points < min_sl_distance_points) {
      sell_sl_price = sell_stop_level + min_sl_distance_points * _Point;
      if(DEBUG_MODE)
         Print("Adjusted sell SL to meet minimum risk/reward");
   }

   // Calculate lot size based on risk management if enabled
   double lots_to_trade = Lots;
   if(RiskManagement_PercentPerTrade > 0) {
      double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      double risk_amount = account_balance * RiskManagement_PercentPerTrade / 100.0;

      // Calculate lot size for buy order
      double buy_risk_points = buy_sl_distance_points;
      double buy_point_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE) *
                              (SymbolInfoDouble(_Symbol, SYMBOL_POINT) / SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE));
      double buy_lots = risk_amount / (buy_risk_points * buy_point_value);

      // Calculate lot size for sell order
      double sell_risk_points = sell_sl_distance_points;
      double sell_point_value = buy_point_value; // Same for both directions
      double sell_lots = risk_amount / (sell_risk_points * sell_point_value);

      // Use the smaller of the two lot sizes to be conservative
      lots_to_trade = MathMin(buy_lots, sell_lots);

      // Normalize lot size
      double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
      double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
      double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

      lots_to_trade = MathMax(min_lot, MathMin(max_lot, MathFloor(lots_to_trade / lot_step) * lot_step));

      if(DEBUG_MODE)
         Print("Risk-based lot size: ", lots_to_trade);
   }

   // Pre-Trigger Confirmation (if enabled)
   if(!IsPreTriggerConfirmationMet()) {
      if(DEBUG_MODE)
         Print("Pre-trigger confirmation not met, skipping order placement");
      return;
   }

   // Place pending orders
   g_pending_order_placement_time = TimeCurrent();

   // Calculate expiration time
   datetime expiration_time = g_pending_order_placement_time + Order_Expiry_Seconds;

   // Place buy stop order
   if(!trade.BuyStop(lots_to_trade, buy_stop_level, _Symbol, buy_sl_price, 0, ORDER_TIME_SPECIFIED, expiration_time, OrderComment)) {
      if(DEBUG_MODE)
         Print("Failed to place buy stop order: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
   } else {
      g_pending_buy_stop_ticket = trade.ResultOrder();
      if(DEBUG_MODE)
         Print("Buy stop placed at ", buy_stop_level, " with SL at ", buy_sl_price, ", ticket: ", g_pending_buy_stop_ticket);
   }

   // Place sell stop order
   if(!trade.SellStop(lots_to_trade, sell_stop_level, _Symbol, sell_sl_price, 0, ORDER_TIME_SPECIFIED, expiration_time, OrderComment)) {
      if(DEBUG_MODE)
         Print("Failed to place sell stop order: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
   } else {
      g_pending_sell_stop_ticket = trade.ResultOrder();
      if(DEBUG_MODE)
         Print("Sell stop placed at ", sell_stop_level, " with SL at ", sell_sl_price, ", ticket: ", g_pending_sell_stop_ticket);
   }

   // Update state
   g_ea_state = PENDING_ORDERS_PLACED;
}

//+------------------------------------------------------------------+
//| Check pending orders expiry                                      |
//+------------------------------------------------------------------+
void CheckPendingOrdersExpiry()
{
   // Check if pending orders have expired
   datetime current_time = TimeCurrent();
   if(current_time - g_pending_order_placement_time >= Order_Expiry_Seconds) {
      // Delete pending orders
      if(g_pending_buy_stop_ticket > 0) {
         if(trade.OrderDelete(g_pending_buy_stop_ticket)) {
            if(DEBUG_MODE)
               Print("Buy stop order expired and deleted: ", g_pending_buy_stop_ticket);
            g_pending_buy_stop_ticket = 0;
         }
      }

      if(g_pending_sell_stop_ticket > 0) {
         if(trade.OrderDelete(g_pending_sell_stop_ticket)) {
            if(DEBUG_MODE)
               Print("Sell stop order expired and deleted: ", g_pending_sell_stop_ticket);
            g_pending_sell_stop_ticket = 0;
         }
      }

      // Reset state
      g_ea_state = IDLE;
   }

   // Check if orders still exist (they might have been filled or deleted)
   bool buy_order_exists = false;
   bool sell_order_exists = false;

   for(int i = 0; i < OrdersTotal(); i++) {
      if(OrderGetTicket(i) > 0) {
         if(OrderGetInteger(ORDER_MAGIC) == MagicNumber && OrderGetString(ORDER_SYMBOL) == _Symbol) {
            if(OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_BUY_STOP && OrderGetInteger(ORDER_TICKET) == g_pending_buy_stop_ticket)
               buy_order_exists = true;

            if(OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_SELL_STOP && OrderGetInteger(ORDER_TICKET) == g_pending_sell_stop_ticket)
               sell_order_exists = true;
         }
      }
   }

   if(!buy_order_exists)
      g_pending_buy_stop_ticket = 0;

   if(!sell_order_exists)
      g_pending_sell_stop_ticket = 0;

   // If both orders are gone but we don't have an active trade, reset state
   if(g_pending_buy_stop_ticket == 0 && g_pending_sell_stop_ticket == 0 && g_active_trade_ticket == 0)
      g_ea_state = IDLE;
}

//+------------------------------------------------------------------+
//| Manage open position                                             |
//+------------------------------------------------------------------+
void ManageOpenPosition()
{
   // Check if position still exists
   bool position_exists = false;

   for(int i = 0; i < PositionsTotal(); i++) {
      if(PositionGetTicket(i) > 0) {
         if(PositionGetInteger(POSITION_MAGIC) == MagicNumber && PositionGetString(POSITION_SYMBOL) == _Symbol) {
            if(PositionGetInteger(POSITION_TICKET) == g_active_trade_ticket) {
               position_exists = true;
               break;
            }
         }
      }
   }

   if(!position_exists) {
      // Position closed, reset variables and enter cooldown
      g_active_trade_ticket = 0;
      g_active_trade_entry_price = 0.0;
      g_active_trade_is_buy = false;
      g_active_trade_initial_SL_price = 0.0;
      g_active_trade_ATR_at_entry_points = 0.0;
      g_active_trade_trailing_activated = false;
      g_active_trade_current_trail_SL = 0.0;
      g_best_price_since_entry = 0.0;
      g_last_new_best_price_time = 0;
      g_bars_in_current_trade = 0;

      g_last_trade_close_time = TimeCurrent();
      g_ea_state = COOLDOWN;

      if(DEBUG_MODE)
         Print("Position closed, entering cooldown state");

      return;
   }

   // Get position details
   double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
   double position_current_sl = PositionGetDouble(POSITION_SL);
   double position_current_tp = PositionGetDouble(POSITION_TP);
   double position_profit = PositionGetDouble(POSITION_PROFIT);
   double position_volume = PositionGetDouble(POSITION_VOLUME);
   datetime position_time = (datetime)PositionGetInteger(POSITION_TIME);
   ENUM_POSITION_TYPE position_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

   // Get current price
   MqlTick latest_tick;
   if(!SymbolInfoTick(_Symbol, latest_tick)) {
      if(DEBUG_MODE)
         Print("Failed to get latest tick data for position management");
      return;
   }

   double current_price = (position_type == POSITION_TYPE_BUY) ? latest_tick.bid : latest_tick.ask;

   // Update best price since entry
   if((position_type == POSITION_TYPE_BUY && current_price > g_best_price_since_entry) ||
      (position_type == POSITION_TYPE_SELL && (g_best_price_since_entry == 0 || current_price < g_best_price_since_entry))) {
      g_best_price_since_entry = current_price;
      g_last_new_best_price_time = TimeCurrent();
   }

   // Calculate profit in points
   double profit_points = 0;
   if(position_type == POSITION_TYPE_BUY)
      profit_points = (current_price - position_open_price) / _Point;
   else
      profit_points = (position_open_price - current_price) / _Point;

   // 1. Trailing Stop Management
   if(!g_active_trade_trailing_activated) {
      // Check if trailing should be activated
      double activation_threshold = 0;

      if(Trail_Activation_Method == ATR_MULT)
         activation_threshold = Trail_Activation_ATR_Mult * g_active_trade_ATR_at_entry_points;
      else // COST_MULT
         activation_threshold = Trail_Activation_Cost_Mult * g_total_cost_per_trade_points;

      if(profit_points >= activation_threshold) {
         g_active_trade_trailing_activated = true;

         // Set initial trailing stop at breakeven plus buffer
         double new_sl = 0;
         if(position_type == POSITION_TYPE_BUY)
            new_sl = position_open_price + (BE_Plus_LockIn_Points * _Point);
         else
            new_sl = position_open_price - (BE_Plus_LockIn_Points * _Point);

         if((position_type == POSITION_TYPE_BUY && new_sl > position_current_sl) ||
            (position_type == POSITION_TYPE_SELL && new_sl < position_current_sl)) {
            if(trade.PositionModify(g_active_trade_ticket, new_sl, position_current_tp)) {
               g_active_trade_current_trail_SL = new_sl;
               if(DEBUG_MODE)
                  Print("Trailing activated, SL moved to breakeven plus buffer: ", new_sl);
            }
         }
      }
   } else {
      // Trailing is active, update trail stop if needed
      double trail_distance = Trail_Stop_Distance_ATR_Mult * g_active_trade_ATR_at_entry_points * _Point;

      // Check for stall condition
      bool is_stalled = false;
      if(Stall_Exit_Enable) {
         datetime current_time = TimeCurrent();
         if(current_time - g_last_new_best_price_time >= Stall_Lookback_Seconds && profit_points > 0) {
            is_stalled = true;
            trail_distance = Stall_Tighten_Trail_ATR_Mult * g_active_trade_ATR_at_entry_points * _Point;
            if(DEBUG_MODE)
               Print("Position stalled, tightening trail distance to ", trail_distance / _Point, " points");
         }
      }

      // Calculate new trail stop
      double new_trail_sl = 0;
      if(position_type == POSITION_TYPE_BUY)
         new_trail_sl = current_price - trail_distance;
      else
         new_trail_sl = current_price + trail_distance;

      // Only update if new SL is better than current SL
      if((position_type == POSITION_TYPE_BUY && new_trail_sl > g_active_trade_current_trail_SL) ||
         (position_type == POSITION_TYPE_SELL && new_trail_sl < g_active_trade_current_trail_SL)) {
         if(trade.PositionModify(g_active_trade_ticket, new_trail_sl, position_current_tp)) {
            g_active_trade_current_trail_SL = new_trail_sl;
            if(DEBUG_MODE)
               Print("Trail SL updated to: ", new_trail_sl);
         }
      }
   }

   // 2. Fixed Take Profit (if enabled)
   if(Enable_Fixed_TP && position_current_tp == 0) {
      double tp_distance = Fixed_TP_ATR_Mult * g_active_trade_ATR_at_entry_points * _Point;
      double tp_level = 0;

      if(position_type == POSITION_TYPE_BUY)
         tp_level = position_open_price + tp_distance;
      else
         tp_level = position_open_price - tp_distance;

      if(trade.PositionModify(g_active_trade_ticket, position_current_sl, tp_level)) {
         if(DEBUG_MODE)
            Print("Fixed TP set at: ", tp_level);
      }
   }

   // 3. Time-Based Exit
   int bars_in_trade = iBarShift(_Symbol, PERIOD_M1, position_time, false);
   g_bars_in_current_trade = bars_in_trade;

   // Check if we should bypass max hold time due to good profit
   bool bypass_max_hold = false;
   if(profit_points > Profit_Target_For_MaxHold_Bypass_Cost_Mult * g_total_cost_per_trade_points) {
      bypass_max_hold = true;
      if(DEBUG_MODE)
         Print("Bypassing max hold time due to good profit: ", profit_points, " points");
   }

   if(!bypass_max_hold && bars_in_trade >= Max_Hold_M1_Bars) {
      if(trade.PositionClose(g_active_trade_ticket)) {
         if(DEBUG_MODE)
            Print("Position closed due to max hold time: ", bars_in_trade, " bars");
      }
   }
}

//+------------------------------------------------------------------+
//| Expert trade transaction function                                |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
   // Handle new position opened from pending order
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD && trans.deal_type == DEAL_TYPE_BUY) {
      // Check if this is our pending buy stop order being filled
      if(trans.order == g_pending_buy_stop_ticket) {
         g_active_trade_ticket = trans.position;
         g_active_trade_entry_price = trans.price;
         g_active_trade_is_buy = true;
         g_active_trade_initial_SL_price = request.sl;
         g_active_trade_ATR_at_entry_points = g_current_M1_ATR_points;
         g_active_trade_trailing_activated = false;
         g_best_price_since_entry = trans.price;
         g_last_new_best_price_time = TimeCurrent();
         g_bars_in_current_trade = 0;

         // Delete the sell stop order since we're now in a buy position
         if(g_pending_sell_stop_ticket > 0) {
            trade.OrderDelete(g_pending_sell_stop_ticket);
            g_pending_sell_stop_ticket = 0;
         }

         g_pending_buy_stop_ticket = 0;
         g_ea_state = IN_TRADE_BUY;

         if(DEBUG_MODE)
            Print("Buy position opened from pending order: Ticket=", g_active_trade_ticket,
                  ", Entry=", g_active_trade_entry_price, ", SL=", g_active_trade_initial_SL_price);
      }
   }
   else if(trans.type == TRADE_TRANSACTION_DEAL_ADD && trans.deal_type == DEAL_TYPE_SELL) {
      // Check if this is our pending sell stop order being filled
      if(trans.order == g_pending_sell_stop_ticket) {
         g_active_trade_ticket = trans.position;
         g_active_trade_entry_price = trans.price;
         g_active_trade_is_buy = false;
         g_active_trade_initial_SL_price = request.sl;
         g_active_trade_ATR_at_entry_points = g_current_M1_ATR_points;
         g_active_trade_trailing_activated = false;
         g_best_price_since_entry = trans.price;
         g_last_new_best_price_time = TimeCurrent();
         g_bars_in_current_trade = 0;

         // Delete the buy stop order since we're now in a sell position
         if(g_pending_buy_stop_ticket > 0) {
            trade.OrderDelete(g_pending_buy_stop_ticket);
            g_pending_buy_stop_ticket = 0;
         }

         g_pending_sell_stop_ticket = 0;
         g_ea_state = IN_TRADE_SELL;

         if(DEBUG_MODE)
            Print("Sell position opened from pending order: Ticket=", g_active_trade_ticket,
                  ", Entry=", g_active_trade_entry_price, ", SL=", g_active_trade_initial_SL_price);
      }
   }
   // Handle position closed
   else if(trans.type == TRADE_TRANSACTION_DEAL_ADD &&
          (trans.deal_type == DEAL_TYPE_SELL || trans.deal_type == DEAL_TYPE_BUY)) {

      // Check if this is an exit deal by querying deal properties
      if(HistoryDealSelect(trans.deal)) {
         ENUM_DEAL_ENTRY deal_entry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(trans.deal, DEAL_ENTRY);
         if(deal_entry != DEAL_ENTRY_OUT) {
            return; // Not an exit deal, skip processing
         }
      } else {
         return; // Could not select deal, skip processing
      }

      // Check if this is our active position being closed
      if(trans.position == g_active_trade_ticket) {
         double profit_points = 0;
         if(g_active_trade_is_buy)
            profit_points = (trans.price - g_active_trade_entry_price) / _Point;
         else
            profit_points = (g_active_trade_entry_price - trans.price) / _Point;

         if(DEBUG_MODE)
            Print("Position closed: Ticket=", g_active_trade_ticket, ", Profit=", profit_points, " points");

         // Reset position variables
         g_active_trade_ticket = 0;
         g_active_trade_entry_price = 0.0;
         g_active_trade_is_buy = false;
         g_active_trade_initial_SL_price = 0.0;
         g_active_trade_ATR_at_entry_points = 0.0;
         g_active_trade_trailing_activated = false;
         g_active_trade_current_trail_SL = 0.0;
         g_best_price_since_entry = 0.0;
         g_last_new_best_price_time = 0;
         g_bars_in_current_trade = 0;

         g_last_trade_close_time = TimeCurrent();
         g_ea_state = COOLDOWN;
      }
   }
}

//+------------------------------------------------------------------+
//| Check pre-trigger confirmation (tick rate and volume surge)      |
//+------------------------------------------------------------------+
bool IsPreTriggerConfirmationMet()
{
   // Check tick rate surge if enabled
   if(TickRate_Surge_Enable) {
      if(g_avg_ticks_per_second == 0) {
         if(DEBUG_MODE)
            Print("Tick rate surge check: No average tick rate calculated yet");
         return false;
      }

      // Calculate current tick rate for the last second
      datetime current_time = TimeCurrent();
      int recent_ticks = 0;

      for(int i = 0; i < ArraySize(g_tick_times); i++) {
         if(g_tick_times[i] > 0 && current_time - g_tick_times[i] <= 1) // Last 1 second
            recent_ticks++;
      }

      double current_tick_rate = recent_ticks;
      double required_tick_rate = g_avg_ticks_per_second * TickRate_Surge_Factor;

      if(current_tick_rate < required_tick_rate) {
         if(DEBUG_MODE)
            Print("Tick rate surge check failed: Current=", current_tick_rate,
                  ", Required=", required_tick_rate, " (avg=", g_avg_ticks_per_second, ")");
         return false;
      }

      if(DEBUG_MODE)
         Print("Tick rate surge confirmed: Current=", current_tick_rate,
               ", Required=", required_tick_rate);
   }

   // Check volume surge if enabled
   if(Volume_Surge_Enable) {
      if(g_M1_VolumeSMA_value == 0) {
         if(DEBUG_MODE)
            Print("Volume surge check: No volume SMA calculated yet");
         return false;
      }

      // Get current M1 bar volume (the most recent completed bar)
      long current_volumes[];
      if(CopyTickVolume(_Symbol, PERIOD_M1, 1, 1, current_volumes) <= 0) {
         if(DEBUG_MODE)
            Print("Volume surge check: Failed to get current bar volume");
         return false;
      }

      double current_volume = (double)current_volumes[0];
      double required_volume = g_M1_VolumeSMA_value * Volume_Surge_Factor;

      if(current_volume < required_volume) {
         if(DEBUG_MODE)
            Print("Volume surge check failed: Current=", current_volume,
                  ", Required=", required_volume, " (SMA=", g_M1_VolumeSMA_value, ")");
         return false;
      }

      if(DEBUG_MODE)
         Print("Volume surge confirmed: Current=", current_volume,
               ", Required=", required_volume);
   }

   return true;
}
