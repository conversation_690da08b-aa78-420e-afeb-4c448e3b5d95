# YAML Prompt for HFT Bot: Dynamic Channel Breakout & Momentum Rider

# Bot Name: HFT_DynamicChannelRider_v1
# Target Instrument: US30 (or broker equivalent like WS30, DJIUSD)
# Target Account Type: Raw Spread (e.g., IC Markets)
# Primary Timeframe: M1
# Concept: Identify dynamic M1 channels, anticipate breakouts confirmed by activity,
#          ride short-term momentum with adaptive trailing stops, factoring in all transaction costs.

#---------------------------------------------------------------------------------------------------
# 1. Global Settings & Inputs
#---------------------------------------------------------------------------------------------------
GlobalSettings:
  MagicNumber: Unique integer (e.g., ********)
  OrderComment: "HFT_DCR_v1"
  Lots:
    type: double
    default: 0.1 # User input for trade volume
    description: Trade volume per order.
  MaxAcceptableSpreadPoints:
    type: int
    default: 150 # (1.5 pips if 100 points = 1 pip)
    description: Maximum live spread (in points) allowed for trading. If current spread exceeds this, no new setups.
  CommissionPerStdLot_Currency:
    type: double
    default: 7.0 # e.g., 7 USD for 1.0 lot round turn
    description: Commission charged by the broker per standard lot for a round turn.
  EstimatedTypicalRawSpreadPoints:
    type: int
    default: 110 # (1.1 pips)
    description: User's observed typical raw spread for the instrument in points.
  RiskManagement_PercentPerTrade:
    type: double
    default: 0.5 # e.g., 0.5% of account balance
    description: Percentage of account equity to risk per trade. If set to 0, fixed 'Lots' input is used.
  StopLevelBufferPoints:
    type: int
    default: 5
    description: Additional points buffer to add to broker's SYMBOL_TRADE_STOPS_LEVEL when placing orders/SL.

#---------------------------------------------------------------------------------------------------
# 2. Session & News Filtering
#---------------------------------------------------------------------------------------------------
SessionFilters:
  EnableSessionFilter:
    type: bool
    default: true
  Session1_StartHour:
    type: int
    default: 7 # GMT
    description: Start hour (broker server time or GMT, specify) for trading session 1.
  Session1_StartMinute:
    type: int
    default: 0
  Session1_EndHour:
    type: int
    default: 9 # GMT
    description: End hour for trading session 1 (e.g., London Open).
  Session1_EndMinute:
    type: int
    default: 0
  Session2_Enable:
    type: bool
    default: true
  Session2_StartHour:
    type: int
    default: 13 # GMT
    description: Start hour for trading session 2.
  Session2_StartMinute:
    type: int
    default: 30
  Session2_EndHour:
    type: int
    default: 17 # GMT
    description: End hour for trading session 2 (e.g., NY Open).
  Session2_EndMinute:
    type: int
    default: 0
  # Note: Implement logic to convert these GMT times to broker server time if necessary.

NewsFilter:
  EnableNewsFilter:
    type: bool
    default: true # Requires manual input of news events or external data feed integration (latter is advanced)
  MinutesBeforeNewsToPause:
    type: int
    default: 20
  MinutesAfterNewsToPause:
    type: int
    default: 20
  # Implementation detail: EA will need a way to get news event times.
  # Simplest: User inputs a comma-separated string of "YYYY.MM.DD HH:MM" for high-impact news.
  # EA checks current time against this list.

#---------------------------------------------------------------------------------------------------
# 3. Overall Market Volatility Filter (Regime Filter)
#---------------------------------------------------------------------------------------------------
OverallVolatilityFilter:
  EnableOverallVolatilityFilter:
    type: bool
    default: true
  HigherTF_ATR_Timeframe:
    type: ENUM_TIMEFRAMES
    default: PERIOD_M15
    description: Timeframe for the higher timeframe ATR.
  HigherTF_ATR_Period:
    type: int
    default: 14
  MinOverallVolatilityATR_Points: # In points
    type: double
    default: 250 # (2.5 pips)
    description: Minimum HigherTF ATR value (in points) required to trade. If below, market too quiet.
  MaxOverallVolatilityATR_Points: # In points
    type: double
    default: 2000 # (20 pips)
    description: Maximum HigherTF ATR value (in points) allowed for trading. If above, market too chaotic.
  # EA updates this check e.g., every 5 minutes.

#---------------------------------------------------------------------------------------------------
# 4. Indicator Settings (M1 Based)
#---------------------------------------------------------------------------------------------------
Indicators_M1:
  M1_ATR_Period:
    type: int
    default: 14 # Slightly longer to smooth for cost basis
  M1_VolumeSMA_Period:
    type: int
    default: 14
  # Optional EMA for directional bias - can be added later if needed
  # M1_EMA_Fast_Period:
  #   type: int
  #   default: 8

#---------------------------------------------------------------------------------------------------
# 5. Dynamic Channel/Range Identification (M1)
#---------------------------------------------------------------------------------------------------
ChannelIdentification:
  Channel_Bars_Lookback:
    type: int
    default: 7 # Number of past M1 bars to define channel
  Min_Channel_ATR_Factor:
    type: double
    default: 0.4 # Channel width must be at least 0.4 * current M1 ATR
    description: Minimum channel width relative to current M1 ATR.
  Min_Channel_Absolute_Points_Factor_Of_Cost: # New
    type: double
    default: 0.75
    description: Channel width must also be at least (TotalCostPerTradePoints * this factor).
  Max_Channel_ATR_Factor:
    type: double
    default: 1.8 # Channel width must not exceed 1.8 * current M1 ATR
    description: Maximum channel width relative to current M1 ATR.

#---------------------------------------------------------------------------------------------------
# 6. Breakout Entry Setup & Trigger (M1)
#---------------------------------------------------------------------------------------------------
EntrySetup:
  Breakout_Offset_ATR_Mult:
    type: double
    default: 0.25 # Pending order offset from channel edge, as multiplier of M1 ATR
  Initial_SL_Buffer_ATR_Mult:
    type: double
    default: 0.25 # SL buffer from opposite channel edge, as multiplier of M1 ATR
  Min_RiskReward_SL_Factor: # New
    type: double
    default: 1.1 # Initial SL distance must be at least (TotalCostPerTradePoints * this factor)
    description: Ensures initial SL isn't too tight relative to entry just to satisfy cost.
  TickRate_Surge_Enable:
    type: bool
    default: true
  TickRate_LookbackSeconds_ForAvg: # New
    type: int
    default: 60
    description: Seconds to look back to calculate average ticks per second.
  TickRate_Surge_Factor:
    type: double
    default: 1.8 # Current ticks/sec must be this factor * average to confirm surge.
  Volume_Surge_Enable:
    type: bool
    default: true
  Volume_Surge_Factor:
    type: double
    default: 1.8 # Triggering bar's volume must be this factor * M1 Volume SMA to confirm.
  Order_Expiry_Seconds:
    type: int
    default: 90 # Pending orders expire if not filled within this time.

#---------------------------------------------------------------------------------------------------
# 7. Active Trade Management (M1)
#---------------------------------------------------------------------------------------------------
TradeManagement:
  # Trailing Stop Loss
  Trail_Activation_Method: # New
    type: enum # (ATR_MULT, COST_MULT)
    default: COST_MULT
    description: How to determine trailing stop activation profit (ATR multiplier or Cost multiplier).
  Trail_Activation_ATR_Mult:
    type: double
    default: 1.0 # Activates trailing if profit > M1_ATR_at_entry * this multiplier. (Used if Trail_Activation_Method is ATR_MULT)
  Trail_Activation_Cost_Mult:
    type: double
    default: 1.75 # Activates trailing if profit > TotalCostPerTradePoints * this multiplier. (Used if Trail_Activation_Method is COST_MULT)
  BE_Plus_LockIn_Points:
    type: int
    default: 20 # Additional points locked in beyond TotalCost when BE+ activates.
    description: Profit points locked in above (TotalCostPerTradePoints) on first trail step.
  Trail_Stop_Distance_ATR_Mult:
    type: double
    default: 0.6 # Trailing stop distance from best price, as multiplier of M1_ATR_at_entry.

  # Optional Fixed Take Profit (If enabled, trailing stop might be disabled or work differently)
  Enable_Fixed_TP:
    type: bool
    default: false
  Fixed_TP_ATR_Mult:
    type: double
    default: 2.0 # TP target from entry, as multiplier of M1_ATR_at_entry.

  # Exit Conditions
  Max_Hold_M1_Bars:
    type: int
    default: 5 # Max M1 bars to hold a trade if not performing.
  Profit_Target_For_MaxHold_Bypass_Cost_Mult: # New
    type: double
    default: 2.5
    description: If trade profit > (TotalCostPerTradePoints * this factor), Max_Hold_M1_Bars rule is bypassed.
  Stall_Exit_Enable:
    type: bool
    default: true
  Stall_Lookback_Seconds:
    type: int
    default: 60 # If no new high/low for this many seconds while in profit.
  Stall_Tighten_Trail_ATR_Mult:
    type: double
    default: 0.15 # Aggressively tighten trail to this M1_ATR_at_entry multiplier if stalled.

#---------------------------------------------------------------------------------------------------
# 8. Post-Trade
#---------------------------------------------------------------------------------------------------
PostTrade:
  Global_Trade_Cooldown_M1_Bars:
    type: int
    default: 3 # M1 bars to wait after any trade closes before looking for new setups.

#---------------------------------------------------------------------------------------------------
# Global Variables (Internal - Not Inputs, but for coder's reference)
#---------------------------------------------------------------------------------------------------
InternalVariables:
  - g_current_M1_ATR_value: double (actual ATR indicator value)
  - g_current_M1_ATR_points: double (ATR value converted to points)
  - g_M1_VolumeSMA_value: double
  - g_HigherTF_ATR_value_points: double
  - g_total_cost_per_trade_points: double (calculated in OnInit from spread + commission)
  - g_commission_per_trade_points_current_lot: double (calculated based on current lot size)
  - g_is_trading_allowed_by_filters: bool (master flag from all global/session/news/volatility checks)
  - g_last_trade_close_time: datetime
  - g_active_trade_ticket: long
  - g_active_trade_entry_price: double
  - g_active_trade_is_buy: bool
  - g_active_trade_initial_SL_price: double
  - g_active_trade_ATR_at_entry_points: double
  - g_active_trade_trailing_activated: bool
  - g_active_trade_current_trail_SL: double
  - g_pending_buy_stop_ticket: long
  - g_pending_sell_stop_ticket: long
  - g_pending_order_placement_time: datetime
  - state_machine: (e.g., IDLE, AWAITING_CHANNEL, PENDING_ORDERS_PLACED, IN_TRADE_BUY, IN_TRADE_SELL, COOLDOWN)

#---------------------------------------------------------------------------------------------------
# Core Logic Flow
#---------------------------------------------------------------------------------------------------
LogicFlow:
  OnInit:
    - Initialize all indicators (M1 ATR, M1 Volume SMA, HigherTF ATR).
    - Calculate `g_commission_per_trade_points_current_lot` based on `Inp_CommissionPerStdLot_Currency`, `Inp_Lots`, and symbol's point value.
    - Calculate `g_total_cost_per_trade_points` = `Inp_EstimatedTypicalRawSpreadPoints` + `g_commission_per_trade_points_current_lot`.
    - Print these calculated costs.
    - Set `trade.SetExpertMagicNumber()`, `trade.SetAsyncMode(true)`, `trade.SetDeviationInPoints()`.

  OnTick:
    - **1. Pre-Flight Checks:**
        - Update `g_is_trading_allowed_by_filters` by checking:
            - Current time against `SessionFilters`.
            - Current time against `NewsFilter` (if enabled and news data available).
            - Current spread against `MaxAcceptableSpreadPoints`.
            - `HigherTF_ATR_value_points` against `Min/MaxOverallVolatilityATR_Points` (update HigherTF ATR less frequently, e.g., on its bar close or every few minutes).
            - `Global_Trade_Cooldown_M1_Bars` using `g_last_trade_close_time`.
        - If `!g_is_trading_allowed_by_filters`, return.
        - If `g_active_trade_ticket > 0`, proceed to `ManageOpenPosition()`, then return.
        - If `g_pending_buy_stop_ticket > 0` or `g_pending_sell_stop_ticket > 0`:
            - Check `Order_Expiry_Seconds`. If expired and not filled, delete pending orders and reset tickets.
            - (Optional) Could trail pending orders slightly if market moves but doesn't trigger, but keep them within breakout logic. This adds complexity. For HFT, simpler to let them expire.

    - **2. Identify Dynamic Channel (if no active trade and not in pending order expiry check):**
        - Get `g_current_M1_ATR_points` and `g_M1_VolumeSMA_value`.
        - Collect highs/lows of last `Channel_Bars_Lookback` M1 bars.
        - Calculate `Channel_High`, `Channel_Low`, `Channel_Width_Points`.
        - Validate channel width using `Min_Channel_ATR_Factor`, `Max_Channel_ATR_Factor`, and `Min_Channel_Absolute_Points_Factor_Of_Cost` (relative to `g_total_cost_per_trade_points`).
        - If channel not valid, return.

    - **3. Setup Breakout Entry (if valid channel):**
        - If `g_pending_buy_stop_ticket == 0` AND `g_pending_sell_stop_ticket == 0`:
            - Get fresh tick: `SymbolInfoTick(_Symbol, latestTick);`
            - Calculate `BuyStop_Level` and `SellStop_Level` using `Breakout_Offset_ATR_Mult`. Ensure they respect `latestTick.ask/bid + stopLevelPoints + StopLevelBufferPoints`.
            - Calculate `Buy_SL_Price` (based on `Channel_Low`) and `Sell_SL_Price` (based on `Channel_High`) using `Initial_SL_Buffer_ATR_Mult`.
            - **Risk Management Calculation (Lot Size):**
                - If `RiskManagement_PercentPerTrade > 0`:
                    - `sl_distance_buy_points = (BuyStop_Level - Buy_SL_Price) / Point`.
                    - `sl_distance_sell_points = (Sell_SL_Price - SellStop_Level) / Point`.
                    - `risk_per_trade_currency = AccountEquity() * (RiskManagement_PercentPerTrade / 100.0)`.
                    - `point_value_for_one_lot = ...` (calculate this robustly, see OnInit notes).
                    - `calculated_lot_size_buy = (risk_per_trade_currency / sl_distance_buy_points) / point_value_for_one_lot`.
                    - `calculated_lot_size_sell = (risk_per_trade_currency / sl_distance_sell_points) / point_value_for_one_lot`.
                    - Use `MathMin(Inp_Lots, NormalizeDouble(calculated_lot_size_buy/sell, lot_step))` capped by MaxLots, respecting MinLots. If calc lot < MinLots, maybe skip or use MinLots if risk allows.
                - Else (fixed lots): Use `Inp_Lots`.
            - **Initial SL Check:** Ensure calculated SL distance (e.g., `sl_distance_buy_points`) > `g_total_cost_per_trade_points * Min_RiskReward_SL_Factor`. If not, consider skipping the setup (or logging a warning if proceeding).
            - **Pre-Trigger Confirmation (if enabled):** Check `TickRate_Surge` and `Volume_Surge` conditions. If not met, do not place orders yet (wait for next tick or M1 bar for volume).
            - If all checks pass:
                - `trade.BuyStop(lot_size, BuyStop_Level, _Symbol, Sell_SL_Price, 0, TimeCurrent() + Order_Expiry_Seconds, ...)` -> store `g_pending_buy_stop_ticket`.
                - `trade.SellStop(lot_size, SellStop_Level, _Symbol, Buy_SL_Price, 0, TimeCurrent() + Order_Expiry_Seconds, ...)` -> store `g_pending_sell_stop_ticket`.
                - Set `g_pending_order_placement_time = TimeCurrent()`.

  ManageOpenPosition (function called from OnTick if `g_active_trade_ticket > 0`):
    - Get fresh tick. Select position by `g_active_trade_ticket`.
    - **1. Adaptive Trailing Stop Logic:**
        - If `!g_active_trade_trailing_activated`:
            - Check if profit has reached `Activation_Profit_Points` (calculated via `Trail_Activation_Method` using `g_active_trade_ATR_at_entry_points` or `g_total_cost_per_trade_points`).
            - If yes:
                - Calculate `new_trail_sl = g_active_trade_entry_price +/- (g_total_cost_per_trade_points + BE_Plus_LockIn_Points * Point)`.
                - Ensure `new_trail_sl` is valid (respects stop level).
                - If `trade.PositionModify(g_active_trade_ticket, new_trail_sl, current_tp)`:
                    - `g_active_trade_trailing_activated = true`.
                    - `g_active_trade_current_trail_SL = new_trail_sl`.
        - Else (trailing is active):
            - Calculate `potential_new_trail_sl` based on current best price achieved and `Trail_Stop_Distance_ATR_Mult * g_active_trade_ATR_at_entry_points`.
            - If `potential_new_trail_sl` is better than `g_active_trade_current_trail_SL` (and diff is > min_sl_move_points):
                - `trade.PositionModify(...)` -> update `g_active_trade_current_trail_SL`.
    - **2. Fixed Take Profit (if `Enable_Fixed_TP`):**
        - If not already set, calculate and set TP using `Fixed_TP_ATR_Mult * g_active_trade_ATR_at_entry_points`. (Be careful if also using trailing SL).
    - **3. Time-Based Exit:**
        - If `Bars(_Symbol, PERIOD_M1, PositionGetInteger(POSITION_TIME_MSC)/1000, TimeCurrent()) >= Max_Hold_M1_Bars`:
            - Check if profit `> g_total_cost_per_trade_points * Profit_Target_For_MaxHold_Bypass_Cost_Mult`.
            - If not significantly in profit, `trade.PositionClose(g_active_trade_ticket)`.
    - **4. Stall Exit (if `Stall_Exit_Enable`):**
        - If in profit (past BE+) and no new price extreme for `Stall_Lookback_Seconds`:
            - Calculate `tightened_sl = current_best_price +/- (g_active_trade_ATR_at_entry_points * Stall_Tighten_Trail_ATR_Mult * Point)`.
            - `trade.PositionModify(...)` with this `tightened_sl`.

  OnTradeTransaction:
    - If `trans.type == TRADE_TRANSACTION_DEAL_ADD` and `trans.entry == DEAL_ENTRY_IN`: (Position Opened)
        - If `trans.order == g_pending_buy_stop_ticket`:
            - Set `g_active_trade_ticket = trans.position`.
            - `g_active_trade_entry_price = trans.price`.
            - `g_active_trade_is_buy = true`.
            - `g_active_trade_initial_SL_price = request.sl` (or re-calc based on actual fill and logic).
            - `g_active_trade_ATR_at_entry_points = g_current_M1_ATR_points` (at time of fill).
            - `g_active_trade_trailing_activated = false`.
            - Delete `g_pending_sell_stop_ticket`. Reset both pending ticket vars.
        - Else if `trans.order == g_pending_sell_stop_ticket`: (Similar logic for sell)
        - (Important) Verify/modify SL immediately to precise calculated level based on actual fill price.
    - If `trans.type == TRADE_TRANSACTION_DEAL_ADD` and `trans.entry == DEAL_ENTRY_OUT`: (Position Closed)
        - `g_last_trade_close_time = TimeCurrent()`.
        - Reset `g_active_trade_ticket = 0` and related active trade vars.
        - Log P/L, hold time, etc.
    - If `trans.type == TRADE_TRANSACTION_ORDER_DELETE`:
        - If `trans.order == g_pending_buy_stop_ticket`, `g_pending_buy_stop_ticket = 0`.
        - If `trans.order == g_pending_sell_stop_ticket`, `g_pending_sell_stop_ticket = 0`.

  OnDeinit:
    - Release indicator handles.
    - (Optional) Close any open positions/pending orders related to this EA.

#---------------------------------------------------------------------------------------------------
# Critical Implementation Notes for Coder
#---------------------------------------------------------------------------------------------------
ImplementationNotes:
  - **Point Value & Cost Conversion:** Robustly calculate the monetary value of 1 point for the current symbol and lot size. All "points" based costs and profit targets need to be consistently applied. The `g_total_cost_per_trade_points` should accurately reflect the cost for the *actually traded lot size*.
  - **Slippage & Requotes:** The EA should handle trade execution errors (requotes, slippage causing SL/TP to be too close) gracefully, log them, and potentially retry (with limits) or skip the trade.
  - **`SymbolInfoTick()`:** Use this *immediately* before any price-sensitive calculation or trade operation.
  - **Normalization:** Normalize all price levels to `_Digits` and lot sizes to `SYMBOL_VOLUME_STEP`.
  - **Error Checking:** Check return values of all trade functions and indicator handle creations.
  - **State Management:** A simple state machine will help manage transitions between looking for setups, pending orders active, and being in a trade.
  - **Debugging:** Include extensive `Print()` statements conditional on a `DEBUG_MODE` input for development and troubleshooting.
  - **Parameter Grouping:** Group inputs logically in the EA's properties window.

---